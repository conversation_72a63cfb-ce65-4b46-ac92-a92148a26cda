<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.gumtree.tns</groupId>
    <artifactId>identity-verification-service</artifactId>
    <version>3.0-SNAPSHOT</version>
  </parent>

  <groupId>com.gumtree.tns.identity-verification-service</groupId>
  <artifactId>server</artifactId>
  <packaging>jar</packaging>
  <name>Identity Verification Service Server</name>
  <description>Identity Verification Service Server</description>

  <properties>
    <HdrHistogram.version>2.1.12</HdrHistogram.version>
    <commons-logging.version>1.2</commons-logging.version>
    <feign.form.version>3.8.0</feign.form.version>
    <jaxb-api.version>2.3.1</jaxb-api.version>
    <jetty-server.version>5.4.9672a23</jetty-server.version>
    <shared-feign-hystrix-api-client.version>2.17.65b4f77</shared-feign-hystrix-api-client.version>
    <shared-gcp-maven-bom-chassis.version>2024-12-10-14-45-56.master.1035670</shared-gcp-maven-bom-chassis.version>
    <swagger-annotations.version>2.2.2</swagger-annotations.version>
    <nimbus-jose-jwt.version>9.0.1</nimbus-jose-jwt.version>
    <gt.shared-commons.properties.version>5.9</gt.shared-commons.properties.version>
    <openapi-generator-maven-plugin.version>5.4.0</openapi-generator-maven-plugin.version>
  </properties>


  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.gumtree.shared-gcp-maven-bom</groupId>
        <artifactId>shared-gcp-maven-bom-chassis</artifactId>
        <version>${shared-gcp-maven-bom-chassis.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <!-- Force CDI API version to resolve dependency convergence issue -->
      <dependency>
        <groupId>javax.enterprise</groupId>
        <artifactId>cdi-api</artifactId>
        <version>1.2</version>
        <scope>provided</scope>
      </dependency>
      <!-- Force HdrHistogram version to resolve dependency convergence issue -->
      <dependency>
        <groupId>org.hdrhistogram</groupId>
        <artifactId>HdrHistogram</artifactId>
        <version>${HdrHistogram.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <dependencies>
    <!-- Internal dependencies -->
    <dependency>
      <groupId>com.gumtree.tns.identity-verification-service</groupId>
      <artifactId>qa-tests</artifactId>
      <version>${project.version}</version>
      <scope>compile</scope>
    </dependency>

    <!-- Spring Boot starters -->
    <dependency>
      <groupId>com.gumtree.shared-gcp-spring-boot</groupId>
      <artifactId>jetty-server</artifactId>
      <version>${jetty-server.version}</version>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-actuator</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-data-jpa</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-validation</artifactId>
    </dependency>

    <!-- Database -->
    <dependency>
      <groupId>org.postgresql</groupId>
      <artifactId>postgresql</artifactId>
    </dependency>
    <dependency>
      <groupId>org.flywaydb</groupId>
      <artifactId>flyway-core</artifactId>
    </dependency>

    <!-- GCP -->
    <dependency>
      <groupId>com.google.cloud</groupId>
      <artifactId>spring-cloud-gcp-starter-trace</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.cloud</groupId>
      <artifactId>spring-cloud-gcp-starter-logging</artifactId>
    </dependency>

    <!-- Logging -->
    <dependency>
      <groupId>com.google.flogger</groupId>
      <artifactId>flogger</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.flogger</groupId>
      <artifactId>flogger-system-backend</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.flogger</groupId>
      <artifactId>flogger-slf4j-backend</artifactId>
    </dependency>

    <!-- Utilities -->
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <optional>true</optional>
    </dependency>

    <!-- Validation -->
    <dependency>
      <groupId>javax.validation</groupId>
      <artifactId>validation-api</artifactId>
    </dependency>

    <!-- JPA and Hibernate -->
    <dependency>
      <groupId>javax.persistence</groupId>
      <artifactId>javax.persistence-api</artifactId>
    </dependency>
    <dependency>
      <groupId>org.hibernate</groupId>
      <artifactId>hibernate-core</artifactId>
    </dependency>
    <dependency>
      <groupId>org.hibernate</groupId>
      <artifactId>hibernate-java8</artifactId>
    </dependency>

    <!-- JSON Processing -->
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.datatype</groupId>
      <artifactId>jackson-datatype-jsr310</artifactId>
    </dependency>

    <!-- OpenAPI -->
    <dependency>
      <groupId>io.swagger.core.v3</groupId>
      <artifactId>swagger-annotations</artifactId>
      <version>2.2.0</version>
    </dependency>

    <!-- HTTP Client -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-webflux</artifactId>
    </dependency>

    <!-- Test dependencies -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.testcontainers</groupId>
      <artifactId>junit-jupiter</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.testcontainers</groupId>
      <artifactId>postgresql</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.h2database</groupId>
      <artifactId>h2</artifactId>
      <scope>test</scope>
    </dependency>


    <dependency>
      <groupId>io.swagger</groupId>
      <artifactId>swagger-annotations</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.code.gson</groupId>
      <artifactId>gson</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.code.findbugs</groupId>
      <artifactId>annotations</artifactId>
    </dependency>
    <dependency>
      <groupId>org.openapitools</groupId>
      <artifactId>jackson-databind-nullable</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-openfeign</artifactId>
    </dependency>

    <!-- Metrics -->
    <dependency>
      <groupId>io.micrometer</groupId>
      <artifactId>micrometer-core</artifactId>
    </dependency>
    <dependency>
      <groupId>io.micrometer</groupId>
      <artifactId>micrometer-registry-prometheus</artifactId>
    </dependency>

    <!-- Testing -->
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-engine</artifactId>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-junit-jupiter</artifactId>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <!-- Spring Boot Maven Plugin -->
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
      </plugin>
      <!-- Override OpenAPI Generator configuration to use correct package structure -->
      <plugin>
        <groupId>org.openapitools</groupId>
        <artifactId>openapi-generator-maven-plugin</artifactId>
        <version>${openapi-generator-maven-plugin.version}</version>
        <executions>
          <execution>
            <id>generate-project-model</id>
            <goals>
              <goal>generate</goal>
            </goals>
            <configuration>
              <inputSpec>${project.build.directory}/contracts/com.gumtree.tns.identity-verification-service.contract.yaml</inputSpec>
              <generatorName>spring</generatorName>
              <output>${project.build.directory}/generated-sources/openapi</output>
              <modelPackage>com.gumtree.tns.identityverification.model</modelPackage>
              <apiPackage>com.gumtree.tns.identityverification.service</apiPackage>
              <generateModels>true</generateModels>
              <generateApis>true</generateApis>
              <generateSupportingFiles>false</generateSupportingFiles>
              <configOptions>
                <interfaceOnly>true</interfaceOnly>
                <skipDefaultInterface>true</skipDefaultInterface>
                <dateLibrary>java8</dateLibrary>
                <serializableModel>true</serializableModel>
                <useTags>true</useTags>
              </configOptions>
              <addCompileSourceRoot>true</addCompileSourceRoot>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>

