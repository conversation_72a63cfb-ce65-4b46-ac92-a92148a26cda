package com.gumtree.exception.handler;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.Mockito.mock;

import lombok.extern.flogger.Flogger;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.context.request.WebRequest;
import com.gumtree.exception.BaseException;
import com.gumtree.tns.identityverification.model.ApiError;

@Flogger
class GenericExceptionHandlerTest {

  @Test
  void handleGenericException() {

    WebRequest webRequest = mock(WebRequest.class);
    GenericExceptionHandler genericExceptionHandler = new GenericExceptionHandler();
    BaseException baseException = new BaseException(new ApiError().detail("some detail"));
    ResponseEntity<ApiError> responseEntity = genericExceptionHandler.handleGenericException(
        baseException, webRequest);

    assertThat(responseEntity.getStatusCode(), is(HttpStatus.INTERNAL_SERVER_ERROR));
    ApiError apiError = responseEntity.getBody();

    log.atInfo().log("ApiError: %s", apiError);
    assertThat(apiError.getDetail(), is("some detail"));

  }
}
