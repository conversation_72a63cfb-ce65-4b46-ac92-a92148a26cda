package com.gumtree.tns.identityverification.service;

import com.gumtree.tns.identityverification.dao.model.IdentityVerificationRequest;
import com.gumtree.tns.identityverification.dao.model.VerificationStatus;
import com.gumtree.tns.identityverification.model.VerificationType;
import com.gumtree.tns.identityverification.dao.repository.IdentityVerificationRequestRepository;
import com.gumtree.tns.identityverification.dao.repository.AuditLogRepository;
import com.gumtree.tns.identityverification.dao.repository.VerificationDocumentRepository;
import com.gumtree.tns.identityverification.dao.repository.RepresentativeRepository;
import com.gumtree.tns.identityverification.dto.SarRequestDto;
import com.gumtree.tns.identityverification.dto.DdrRequestDto;
import com.gumtree.tns.identityverification.service.impl.GdprServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for GdprService
 */
@ExtendWith(MockitoExtension.class)
class GdprServiceTest {

    @Mock
    private IdentityVerificationRequestRepository verificationRequestRepository;

    @Mock
    private AuditLogRepository auditLogRepository;

    @Mock
    private VerificationDocumentRepository documentRepository;

    @Mock
    private RepresentativeRepository representativeRepository;

    @Mock
    private UserLookupService userLookupService;

    @Mock
    private AuditLogService auditLogService;

    private ObjectMapper objectMapper;
    private MeterRegistry meterRegistry;
    private GdprServiceImpl gdprService;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        meterRegistry = new SimpleMeterRegistry();
        
        gdprService = new GdprServiceImpl(
            verificationRequestRepository,
            auditLogRepository,
            documentRepository,
            representativeRepository,
            userLookupService,
            auditLogService,
            objectMapper,
            meterRegistry
        );
    }

    @Test
    void processSubjectAccessRequest_Success() {
        // Given
        SarRequestDto sarRequest = SarRequestDto.builder()
            .requestId("REQ-123")
            .subtaskId("SUB-456")
            .email("<EMAIL>")
            .build();

        List<Long> userIds = Arrays.asList(12345L);
        List<Long> accountIds = Arrays.asList();

        IdentityVerificationRequest verificationRequest = IdentityVerificationRequest.builder()
            .id(UUID.randomUUID())
            .userId(12345L)
            .verificationType(VerificationType.KYC)
            .status(VerificationStatus.COMPLETED)
            .riskScore(25)
            .createdAt(LocalDateTime.now())
            .build();

        when(userLookupService.findUserIdsByEmail("<EMAIL>"))
            .thenReturn(userIds);
        when(userLookupService.findAccountIdsByEmail("<EMAIL>"))
            .thenReturn(accountIds);
        when(verificationRequestRepository.findByUserId(12345L))
            .thenReturn(Arrays.asList(verificationRequest));
        when(auditLogRepository.findByUserId(12345L))
            .thenReturn(Arrays.asList());

        // When
        var result = gdprService.processSubjectAccessRequest(sarRequest);

        // Then
        assertNotNull(result);
        assertEquals("REQ-123", result.getRequestId());
        assertEquals("SUB-456", result.getSubtaskId());
        assertFalse(result.isError());
        assertNotNull(result.getPersonalDataSegments());
        assertFalse(result.getPersonalDataSegments().isEmpty());
        
        verify(auditLogService).logSarProcessed(eq("REQ-123"), eq("<EMAIL>"), anyInt());
    }

    @Test
    void processSubjectAccessRequest_NoDataFound() {
        // Given
        SarRequestDto sarRequest = SarRequestDto.builder()
            .requestId("REQ-123")
            .subtaskId("SUB-456")
            .email("<EMAIL>")
            .build();

        when(userLookupService.findUserIdsByEmail("<EMAIL>"))
            .thenReturn(Arrays.asList());
        when(userLookupService.findAccountIdsByEmail("<EMAIL>"))
            .thenReturn(Arrays.asList());

        // When
        var result = gdprService.processSubjectAccessRequest(sarRequest);

        // Then
        assertNotNull(result);
        assertEquals("REQ-123", result.getRequestId());
        assertEquals("SUB-456", result.getSubtaskId());
        assertFalse(result.isError());
        assertTrue(result.getPersonalDataSegments().isEmpty());
    }

    @Test
    void processSubjectAccessRequest_InvalidRequest() {
        // Given
        SarRequestDto sarRequest = SarRequestDto.builder()
            .requestId("") // Invalid request ID
            .subtaskId("SUB-456")
            .email("<EMAIL>")
            .build();

        // When
        var result = gdprService.processSubjectAccessRequest(sarRequest);

        // Then
        assertNotNull(result);
        assertTrue(result.isError());
        assertEquals("Invalid GDPR request", result.getReason());
    }

    @Test
    void processDataDeletionRequest_Success() {
        // Given
        DdrRequestDto ddrRequest = DdrRequestDto.builder()
            .requestId("REQ-123")
            .subtaskId("SUB-456")
            .email("<EMAIL>")
            .build();

        List<Long> userIds = Arrays.asList(12345L);
        List<Long> accountIds = Arrays.asList();

        IdentityVerificationRequest verificationRequest = IdentityVerificationRequest.builder()
            .id(UUID.randomUUID())
            .userId(12345L)
            .verificationType(VerificationType.KYC)
            .status(VerificationStatus.COMPLETED)
            .build();

        when(userLookupService.findUserIdsByEmail("<EMAIL>"))
            .thenReturn(userIds);
        when(userLookupService.findAccountIdsByEmail("<EMAIL>"))
            .thenReturn(accountIds);
        when(verificationRequestRepository.findByUserId(12345L))
            .thenReturn(Arrays.asList(verificationRequest));
        when(auditLogRepository.findByUserId(12345L))
            .thenReturn(Arrays.asList());

        // When
        var result = gdprService.processDataDeletionRequest(ddrRequest);

        // Then
        assertNotNull(result);
        assertEquals("REQ-123", result.getRequestId());
        assertEquals("SUB-456", result.getSubtaskId());
        assertFalse(result.isError());
        
        verify(documentRepository).deleteByVerificationRequestId(verificationRequest.getId());
        verify(representativeRepository).deleteByVerificationRequestId(verificationRequest.getId());
        verify(verificationRequestRepository).delete(verificationRequest);
        verify(auditLogService).logDdrProcessed(eq("REQ-123"), eq("<EMAIL>"), anyInt());
    }

    @Test
    void processDataDeletionRequest_NoDataFound() {
        // Given
        DdrRequestDto ddrRequest = DdrRequestDto.builder()
            .requestId("REQ-123")
            .subtaskId("SUB-456")
            .email("<EMAIL>")
            .build();

        when(userLookupService.findUserIdsByEmail("<EMAIL>"))
            .thenReturn(Arrays.asList());
        when(userLookupService.findAccountIdsByEmail("<EMAIL>"))
            .thenReturn(Arrays.asList());

        // When
        var result = gdprService.processDataDeletionRequest(ddrRequest);

        // Then
        assertNotNull(result);
        assertEquals("REQ-123", result.getRequestId());
        assertEquals("SUB-456", result.getSubtaskId());
        assertFalse(result.isError());
        
        verify(verificationRequestRepository, never()).delete(any());
    }

    @Test
    void hasUserVerificationData_True() {
        // Given
        String email = "<EMAIL>";
        List<Long> userIds = Arrays.asList(12345L);
        
        IdentityVerificationRequest verificationRequest = IdentityVerificationRequest.builder()
            .id(UUID.randomUUID())
            .userId(12345L)
            .build();

        when(userLookupService.findUserIdsByEmail(email))
            .thenReturn(userIds);
        when(userLookupService.findAccountIdsByEmail(email))
            .thenReturn(Arrays.asList());
        when(verificationRequestRepository.findTopByUserIdOrderByCreatedAtDesc(12345L))
            .thenReturn(java.util.Optional.of(verificationRequest));

        // When
        boolean result = gdprService.hasUserVerificationData(email);

        // Then
        assertTrue(result);
    }

    @Test
    void hasUserVerificationData_False() {
        // Given
        String email = "<EMAIL>";
        
        when(userLookupService.findUserIdsByEmail(email))
            .thenReturn(Arrays.asList());
        when(userLookupService.findAccountIdsByEmail(email))
            .thenReturn(Arrays.asList());

        // When
        boolean result = gdprService.hasUserVerificationData(email);

        // Then
        assertFalse(result);
    }

    @Test
    void getUserVerificationDataCount_Success() {
        // Given
        String email = "<EMAIL>";
        List<Long> userIds = Arrays.asList(12345L);
        List<Long> accountIds = Arrays.asList(67890L);

        when(userLookupService.findUserIdsByEmail(email))
            .thenReturn(userIds);
        when(userLookupService.findAccountIdsByEmail(email))
            .thenReturn(accountIds);
        when(verificationRequestRepository.countByUserId(12345L))
            .thenReturn(2L);
        when(verificationRequestRepository.countByAccountId(67890L))
            .thenReturn(1L);

        // When
        long result = gdprService.getUserVerificationDataCount(email);

        // Then
        assertEquals(3L, result);
    }

    @Test
    void anonymizeUserData_Success() {
        // Given
        String email = "<EMAIL>";
        List<Long> userIds = Arrays.asList(12345L);
        
        IdentityVerificationRequest verificationRequest = IdentityVerificationRequest.builder()
            .id(UUID.randomUUID())
            .userId(12345L)
            .personalInfo("{\"firstName\":\"John\",\"lastName\":\"Doe\"}")
            .customerReference("user_12345_kyc")
            .build();

        when(userLookupService.findUserIdsByEmail(email))
            .thenReturn(userIds);
        when(userLookupService.findAccountIdsByEmail(email))
            .thenReturn(Arrays.asList());
        when(verificationRequestRepository.findByUserId(12345L))
            .thenReturn(Arrays.asList(verificationRequest));
        when(auditLogRepository.findByUserId(12345L))
            .thenReturn(Arrays.asList());

        // When
        boolean result = gdprService.anonymizeUserData(email);

        // Then
        assertTrue(result);
        verify(verificationRequestRepository).save(argThat(req -> 
            req.getPersonalInfo() == null && 
            "ANONYMIZED".equals(req.getCustomerReference())));
    }

    @Test
    void validateGdprRequest_Valid() {
        // When
        boolean result = gdprService.validateGdprRequest("REQ-123", "<EMAIL>");

        // Then
        assertTrue(result);
    }

    @Test
    void validateGdprRequest_InvalidRequestId() {
        // When
        boolean result = gdprService.validateGdprRequest("", "<EMAIL>");

        // Then
        assertFalse(result);
    }

    @Test
    void validateGdprRequest_InvalidEmail() {
        // When
        boolean result = gdprService.validateGdprRequest("REQ-123", "invalid-email");

        // Then
        assertFalse(result);
    }

    @Test
    void exportUserDataAsJson_Success() {
        // Given
        String email = "<EMAIL>";
        List<Long> userIds = Arrays.asList(12345L);
        
        IdentityVerificationRequest verificationRequest = IdentityVerificationRequest.builder()
            .id(UUID.randomUUID())
            .userId(12345L)
            .verificationType(VerificationType.KYC)
            .status(VerificationStatus.COMPLETED)
            .riskScore(25)
            .createdAt(LocalDateTime.now())
            .build();

        when(userLookupService.findUserIdsByEmail(email))
            .thenReturn(userIds);
        when(userLookupService.findAccountIdsByEmail(email))
            .thenReturn(Arrays.asList());
        when(verificationRequestRepository.findByUserId(12345L))
            .thenReturn(Arrays.asList(verificationRequest));

        // When
        String result = gdprService.exportUserDataAsJson(email);

        // Then
        assertNotNull(result);
        assertNotEquals("{}", result);
        assertTrue(result.contains("\"email\":\"<EMAIL>\""));
    }
}
