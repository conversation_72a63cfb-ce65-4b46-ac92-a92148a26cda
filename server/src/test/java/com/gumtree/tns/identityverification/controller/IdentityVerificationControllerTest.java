package com.gumtree.tns.identityverification.controller;

import com.gumtree.tns.identityverification.dto.InitiateVerificationRequestDto;
import com.gumtree.tns.identityverification.dto.VerificationResponseDto;
import com.gumtree.tns.identityverification.dto.VerificationStatusResponseDto;
import com.gumtree.tns.identityverification.dao.model.VerificationStatus;
import com.gumtree.tns.identityverification.model.VerificationType;
import com.gumtree.tns.identityverification.dao.model.RiskLevel;
import com.gumtree.tns.identityverification.service.IdentityVerificationService;
import com.gumtree.tns.identityverification.service.DocumentService;
import com.gumtree.tns.identityverification.service.mapper.VerificationControllerMapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Unit tests for IdentityVerificationController
 */
@WebMvcTest(IdentityVerificationController.class)
class IdentityVerificationControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private IdentityVerificationService identityVerificationService;

    @MockBean
    private DocumentService documentService;

    @MockBean
    private VerificationControllerMapper controllerMapper;

    @MockBean
    private MeterRegistry meterRegistry;

    private VerificationResponseDto verificationResponse;
    private VerificationStatusResponseDto statusResponse;

    @BeforeEach
    void setUp() {
        verificationResponse = VerificationResponseDto.builder()
            .verificationId(UUID.randomUUID())
            .status(VerificationStatus.INITIATED)
            .gbgProfileId("gbg-profile-123")
            .createdAt(LocalDateTime.now())
            .build();

        statusResponse = VerificationStatusResponseDto.builder()
            .verificationId(UUID.randomUUID())
            .userId(12345L)
            .status(VerificationStatus.COMPLETED)
            .riskScore(25)
            .riskLevel(RiskLevel.LOW)
            .createdAt(LocalDateTime.now())
            .updatedAt(LocalDateTime.now())
            .completedAt(LocalDateTime.now())
            .build();

        // Mock meter registry to return simple counters and timers
        when(meterRegistry.counter(anyString())).thenReturn(new SimpleMeterRegistry().counter("test"));
        when(meterRegistry.timer(anyString())).thenReturn(new SimpleMeterRegistry().timer("test"));
    }

    @Test
    void initiateUserIdentityVerification_Success() throws Exception {
        // Given
        Long userId = 12345L;
        var request = new com.gumtree.tns.identityverification.model.InitiateVerificationRequest()
            .verificationType(com.gumtree.tns.identityverification.model.VerificationType.KYC)
            .customerReference("user_12345_kyc");

        var requestDto = InitiateVerificationRequestDto.builder()
            .verificationType(VerificationType.KYC)
            .customerReference("user_12345_kyc")
            .build();

        when(controllerMapper.toInitiateVerificationRequestDto(any(com.gumtree.tns.identityverification.model.InitiateVerificationRequest.class)))
            .thenReturn(requestDto);
        when(identityVerificationService.initiateUserVerification(eq(userId), any()))
            .thenReturn(verificationResponse);
        when(controllerMapper.toVerificationResponse(any()))
            .thenReturn(new com.gumtree.tns.identityverification.model.VerificationResponse()
                .verificationId(verificationResponse.getVerificationId())
                .status(com.gumtree.tns.identityverification.model.VerificationStatus.INITIATED));

        // When & Then
        mockMvc.perform(post("/users/{userId}/identity-verification", userId)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.verificationId").exists())
                .andExpect(jsonPath("$.status").value("INITIATED"));

        verify(identityVerificationService).initiateUserVerification(eq(userId), any());
    }

    @Test
    void initiateUserIdentityVerification_ValidationError() throws Exception {
        // Given
        Long userId = 12345L;
        var request = new com.gumtree.tns.identityverification.model.InitiateVerificationRequest()
            .verificationType(null); // Missing required field

        // When & Then
        mockMvc.perform(post("/users/{userId}/identity-verification", userId)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());

        verify(identityVerificationService, never()).initiateUserVerification(any(), any());
    }

    @Test
    void getUserVerificationStatus_Found() throws Exception {
        // Given
        Long userId = 12345L;

        when(identityVerificationService.getUserVerificationStatus(userId))
            .thenReturn(Optional.of(statusResponse));
        when(controllerMapper.toVerificationStatusResponse(any()))
            .thenReturn(new com.gumtree.tns.identityverification.model.VerificationStatusResponse()
                .verificationId(statusResponse.getVerificationId())
                .userId(statusResponse.getUserId())
                .status(com.gumtree.tns.identityverification.model.VerificationStatus.COMPLETED)
                .riskScore(statusResponse.getRiskScore()));

        // When & Then
        mockMvc.perform(get("/users/{userId}/identity-verification", userId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.verificationId").exists())
                .andExpect(jsonPath("$.userId").value(userId))
                .andExpect(jsonPath("$.status").value("COMPLETED"))
                .andExpect(jsonPath("$.riskScore").value(25));

        verify(identityVerificationService).getUserVerificationStatus(userId);
    }

    @Test
    void getUserVerificationStatus_NotFound() throws Exception {
        // Given
        Long userId = 12345L;

        when(identityVerificationService.getUserVerificationStatus(userId))
            .thenReturn(Optional.empty());

        // When & Then
        mockMvc.perform(get("/users/{userId}/identity-verification", userId))
                .andExpect(status().isNotFound());

        verify(identityVerificationService).getUserVerificationStatus(userId);
    }

    @Test
    void initiateAccountIdentityVerification_Success() throws Exception {
        // Given
        Long accountId = 67890L;
        var request = new com.gumtree.tns.identityverification.model.InitiateBusinessVerificationRequest()
            .verificationType(com.gumtree.tns.identityverification.model.VerificationType.KYB)
            .customerReference("account_67890_kyb");

        var requestDto = InitiateVerificationRequestDto.builder()
            .verificationType(VerificationType.KYB)
            .customerReference("account_67890_kyb")
            .build();

        when(controllerMapper.toInitiateVerificationRequestDto(any(com.gumtree.tns.identityverification.model.InitiateBusinessVerificationRequest.class)))
            .thenReturn(requestDto);
        when(identityVerificationService.initiateAccountVerification(eq(accountId), any()))
            .thenReturn(verificationResponse);
        when(controllerMapper.toVerificationResponse(any()))
            .thenReturn(new com.gumtree.tns.identityverification.model.VerificationResponse()
                .verificationId(verificationResponse.getVerificationId())
                .status(com.gumtree.tns.identityverification.model.VerificationStatus.INITIATED));

        // When & Then
        mockMvc.perform(post("/accounts/{accountId}/identity-verification", accountId)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.verificationId").exists())
                .andExpect(jsonPath("$.status").value("INITIATED"));

        verify(identityVerificationService).initiateAccountVerification(eq(accountId), any());
    }

    @Test
    void getAccountVerificationStatus_Found() throws Exception {
        // Given
        Long accountId = 67890L;
        var accountStatusResponse = VerificationStatusResponseDto.builder()
            .verificationId(UUID.randomUUID())
            .accountId(accountId)
            .status(VerificationStatus.APPROVED)
            .riskScore(15)
            .riskLevel(RiskLevel.LOW)
            .createdAt(LocalDateTime.now())
            .build();

        when(identityVerificationService.getAccountVerificationStatus(accountId))
            .thenReturn(Optional.of(accountStatusResponse));
        when(controllerMapper.toVerificationStatusResponse(any()))
            .thenReturn(new com.gumtree.tns.identityverification.model.VerificationStatusResponse()
                .verificationId(accountStatusResponse.getVerificationId())
                .accountId(accountStatusResponse.getAccountId())
                .status(com.gumtree.tns.identityverification.model.VerificationStatus.APPROVED)
                .riskScore(accountStatusResponse.getRiskScore()));

        // When & Then
        mockMvc.perform(get("/accounts/{accountId}/identity-verification", accountId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.verificationId").exists())
                .andExpect(jsonPath("$.accountId").value(accountId))
                .andExpect(jsonPath("$.status").value("APPROVED"))
                .andExpect(jsonPath("$.riskScore").value(15));

        verify(identityVerificationService).getAccountVerificationStatus(accountId);
    }

    @Test
    void submitVerificationRequest_Success() throws Exception {
        // Given
        UUID verificationId = UUID.randomUUID();
        var submittedResponse = VerificationResponseDto.builder()
            .verificationId(verificationId)
            .status(VerificationStatus.IN_PROGRESS)
            .gbgProfileId("gbg-profile-123")
            .createdAt(LocalDateTime.now())
            .build();

        when(identityVerificationService.submitVerificationRequest(verificationId))
            .thenReturn(submittedResponse);
        when(controllerMapper.toVerificationResponse(any()))
            .thenReturn(new com.gumtree.tns.identityverification.model.VerificationResponse()
                .verificationId(submittedResponse.getVerificationId())
                .status(com.gumtree.tns.identityverification.model.VerificationStatus.IN_PROGRESS));

        // When & Then
        mockMvc.perform(post("/verification-requests/{verificationId}/submit", verificationId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.verificationId").value(verificationId.toString()))
                .andExpect(jsonPath("$.status").value("IN_PROGRESS"));

        verify(identityVerificationService).submitVerificationRequest(verificationId);
    }

    @Test
    void submitVerificationRequest_NotFound() throws Exception {
        // Given
        UUID verificationId = UUID.randomUUID();

        when(identityVerificationService.submitVerificationRequest(verificationId))
            .thenThrow(new IllegalArgumentException("Verification request not found: " + verificationId));

        // When & Then
        mockMvc.perform(post("/verification-requests/{verificationId}/submit", verificationId))
                .andExpect(status().isBadRequest());

        verify(identityVerificationService).submitVerificationRequest(verificationId);
    }

    @Test
    void submitVerificationRequest_InvalidState() throws Exception {
        // Given
        UUID verificationId = UUID.randomUUID();

        when(identityVerificationService.submitVerificationRequest(verificationId))
            .thenThrow(new IllegalStateException("Verification request cannot be submitted in current state"));

        // When & Then
        mockMvc.perform(post("/verification-requests/{verificationId}/submit", verificationId))
                .andExpect(status().isConflict());

        verify(identityVerificationService).submitVerificationRequest(verificationId);
    }
}
