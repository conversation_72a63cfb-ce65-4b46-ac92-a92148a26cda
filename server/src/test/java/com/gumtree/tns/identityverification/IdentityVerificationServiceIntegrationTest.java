package com.gumtree.tns.identityverification;

import com.gumtree.tns.identityverification.dao.model.IdentityVerificationRequest;
import com.gumtree.tns.identityverification.dao.model.VerificationStatus;
import com.gumtree.tns.identityverification.model.VerificationType;
import com.gumtree.tns.identityverification.dao.repository.IdentityVerificationRequestRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Integration tests for Identity Verification Service
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
class IdentityVerificationServiceIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private IdentityVerificationRequestRepository verificationRequestRepository;

    @Test
    void fullVerificationWorkflow_Success() throws Exception {
        // Step 1: Initiate user verification
        Long userId = 12345L;
        Map<String, Object> initiateRequest = new HashMap<>();
        initiateRequest.put("verificationType", "KYC");
        initiateRequest.put("customerReference", "user_12345_kyc");
        
        Map<String, Object> personalInfo = new HashMap<>();
        personalInfo.put("firstName", "John");
        personalInfo.put("lastName", "Doe");
        personalInfo.put("dateOfBirth", "1990-01-15");
        personalInfo.put("nationality", "British");
        initiateRequest.put("personalInfo", personalInfo);

        var initiateResult = mockMvc.perform(post("/users/{userId}/identity-verification", userId)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(initiateRequest)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.verificationId").exists())
                .andExpect(jsonPath("$.status").value("INITIATED"))
                .andReturn();

        // Extract verification ID from response
        String responseContent = initiateResult.getResponse().getContentAsString();
        Map<String, Object> responseMap = objectMapper.readValue(responseContent, Map.class);
        String verificationId = (String) responseMap.get("verificationId");

        // Step 2: Check verification status
        mockMvc.perform(get("/users/{userId}/identity-verification", userId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.verificationId").value(verificationId))
                .andExpect(jsonPath("$.userId").value(userId))
                .andExpect(jsonPath("$.status").value("INITIATED"));

        // Step 3: Upload document
        Map<String, Object> documentRequest = new HashMap<>();
        documentRequest.put("documentType", "PASSPORT");
        documentRequest.put("file", "base64-encoded-file-content");
        documentRequest.put("description", "Passport document for verification");

        mockMvc.perform(post("/verification-requests/{verificationId}/documents", verificationId)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(documentRequest)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.documentId").exists())
                .andExpect(jsonPath("$.documentType").value("PASSPORT"))
                .andExpect(jsonPath("$.status").value("UPLOADED"));

        // Step 4: Submit verification request
        mockMvc.perform(post("/verification-requests/{verificationId}/submit", verificationId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.verificationId").value(verificationId))
                .andExpect(jsonPath("$.status").value("IN_PROGRESS"));

        // Step 5: Check final status
        mockMvc.perform(get("/users/{userId}/identity-verification", userId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.verificationId").value(verificationId))
                .andExpect(jsonPath("$.status").value("IN_PROGRESS"));
    }

    @Test
    void accountVerificationWorkflow_Success() throws Exception {
        // Step 1: Initiate account verification
        Long accountId = 67890L;
        Map<String, Object> initiateRequest = new HashMap<>();
        initiateRequest.put("verificationType", "KYB");
        initiateRequest.put("customerReference", "account_67890_kyb");
        
        Map<String, Object> businessInfo = new HashMap<>();
        businessInfo.put("companyName", "Acme Corporation Ltd");
        businessInfo.put("companyNumber", "********");
        businessInfo.put("domain", "acme.com");
        
        Map<String, Object> address = new HashMap<>();
        address.put("street", "123 Business Street");
        address.put("city", "London");
        address.put("postalCode", "SW1A 1AA");
        address.put("countryCode", "GB");
        businessInfo.put("address", address);
        
        initiateRequest.put("businessInfo", businessInfo);

        var initiateResult = mockMvc.perform(post("/accounts/{accountId}/identity-verification", accountId)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(initiateRequest)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.verificationId").exists())
                .andExpect(jsonPath("$.status").value("INITIATED"))
                .andReturn();

        // Extract verification ID from response
        String responseContent = initiateResult.getResponse().getContentAsString();
        Map<String, Object> responseMap = objectMapper.readValue(responseContent, Map.class);
        String verificationId = (String) responseMap.get("verificationId");

        // Step 2: Check verification status
        mockMvc.perform(get("/accounts/{accountId}/identity-verification", accountId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.verificationId").value(verificationId))
                .andExpect(jsonPath("$.accountId").value(accountId))
                .andExpect(jsonPath("$.status").value("INITIATED"));
    }

    @Test
    void gdprWorkflow_Success() throws Exception {
        // Step 1: Create test data
        IdentityVerificationRequest testRequest = IdentityVerificationRequest.builder()
            .id(UUID.randomUUID())
            .userId(12345L)
            .verificationType(VerificationType.KYC)
            .status(VerificationStatus.COMPLETED)
            .personalInfo("{\"firstName\":\"John\",\"lastName\":\"Doe\"}")
            .customerReference("user_12345_kyc")
            .riskScore(25)
            .createdAt(LocalDateTime.now())
            .updatedAt(LocalDateTime.now())
            .completedAt(LocalDateTime.now())
            .build();
        
        verificationRequestRepository.save(testRequest);

        // Step 2: Subject Access Request
        Map<String, Object> sarRequest = new HashMap<>();
        sarRequest.put("requestId", "REQ-123");
        sarRequest.put("subtaskId", "SUB-456");
        sarRequest.put("email", "<EMAIL>");

        mockMvc.perform(put("/gdpr/sar")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(sarRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.requestId").value("REQ-123"))
                .andExpect(jsonPath("$.subtaskId").value("SUB-456"))
                .andExpect(jsonPath("$.error").value(false))
                .andExpect(jsonPath("$.personalDataSegments").isArray());

        // Step 3: Data Deletion Request
        Map<String, Object> ddrRequest = new HashMap<>();
        ddrRequest.put("requestId", "REQ-124");
        ddrRequest.put("subtaskId", "SUB-457");
        ddrRequest.put("email", "<EMAIL>");

        mockMvc.perform(put("/gdpr/ddr")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(ddrRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.requestId").value("REQ-124"))
                .andExpect(jsonPath("$.subtaskId").value("SUB-457"))
                .andExpect(jsonPath("$.error").value(false));
    }

    @Test
    void adminWorkflow_Success() throws Exception {
        // Step 1: Create test data
        IdentityVerificationRequest testRequest1 = IdentityVerificationRequest.builder()
            .id(UUID.randomUUID())
            .userId(12345L)
            .verificationType(VerificationType.KYC)
            .status(VerificationStatus.UNDER_REVIEW)
            .riskScore(75)
            .createdAt(LocalDateTime.now())
            .build();

        IdentityVerificationRequest testRequest2 = IdentityVerificationRequest.builder()
            .id(UUID.randomUUID())
            .accountId(67890L)
            .verificationType(VerificationType.KYB)
            .status(VerificationStatus.COMPLETED)
            .riskScore(20)
            .createdAt(LocalDateTime.now())
            .build();

        verificationRequestRepository.save(testRequest1);
        verificationRequestRepository.save(testRequest2);

        // Step 2: Get all verification requests
        mockMvc.perform(get("/admin/verification-requests")
                .param("page", "0")
                .param("size", "20"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.totalElements").exists())
                .andExpect(jsonPath("$.totalPages").exists());

        // Step 3: Filter by status
        mockMvc.perform(get("/admin/verification-requests")
                .param("status", "UNDER_REVIEW")
                .param("page", "0")
                .param("size", "20"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray());

        // Step 4: Filter by user ID
        mockMvc.perform(get("/admin/verification-requests")
                .param("userId", "12345")
                .param("page", "0")
                .param("size", "20"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray());
    }

    @Test
    void errorHandling_ValidationErrors() throws Exception {
        // Test validation error for missing required fields
        Long userId = 12345L;
        Map<String, Object> invalidRequest = new HashMap<>();
        // Missing verificationType

        mockMvc.perform(post("/users/{userId}/identity-verification", userId)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(invalidRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.error").value("Validation Failed"));
    }

    @Test
    void errorHandling_NotFound() throws Exception {
        // Test not found error
        Long nonExistentUserId = 99999L;

        mockMvc.perform(get("/users/{userId}/identity-verification", nonExistentUserId))
                .andExpect(status().isNotFound());
    }
}
