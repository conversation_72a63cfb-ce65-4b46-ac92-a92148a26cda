package com.gumtree.tns.identityverification.service;

import com.gumtree.tns.identityverification.dao.model.IdentityVerificationRequest;
import com.gumtree.tns.identityverification.dao.model.VerificationStatus;
import com.gumtree.tns.identityverification.model.VerificationType;
import com.gumtree.tns.identityverification.dao.repository.IdentityVerificationRequestRepository;
import com.gumtree.tns.identityverification.dto.InitiateVerificationRequestDto;
import com.gumtree.tns.identityverification.dto.PersonalInfoDto;
import com.gumtree.tns.identityverification.gateway.GbgGateway;
import com.gumtree.tns.identityverification.service.impl.IdentityVerificationServiceImpl;
import com.gumtree.tns.identityverification.service.mapper.VerificationMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for IdentityVerificationService
 */
@ExtendWith(MockitoExtension.class)
class IdentityVerificationServiceTest {

    @Mock
    private IdentityVerificationRequestRepository verificationRequestRepository;

    @Mock
    private GbgGateway gbgGateway;

    @Mock
    private RiskAssessmentService riskAssessmentService;

    @Mock
    private AuditLogService auditLogService;

    @Mock
    private VerificationMapper verificationMapper;

    private IdentityVerificationServiceImpl identityVerificationService;

    @BeforeEach
    void setUp() {
        identityVerificationService = new IdentityVerificationServiceImpl(
            verificationRequestRepository,
            gbgGateway,
            riskAssessmentService,
            auditLogService,
            verificationMapper
        );
    }

    @Test
    void initiateUserVerification_Success() {
        // Given
        Long userId = 12345L;
        InitiateVerificationRequestDto request = InitiateVerificationRequestDto.builder()
            .verificationType(VerificationType.KYC)
            .personalInfo(PersonalInfoDto.builder()
                .firstName("John")
                .lastName("Doe")
                .build())
            .customerReference("user_12345_kyc")
            .build();

        IdentityVerificationRequest savedRequest = IdentityVerificationRequest.builder()
            .id(UUID.randomUUID())
            .userId(userId)
            .verificationType(VerificationType.KYC)
            .status(VerificationStatus.INITIATED)
            .gbgProfileId("gbg-profile-123")
            .createdAt(LocalDateTime.now())
            .build();

        when(verificationRequestRepository.findByUserIdAndStatusIn(eq(userId), any()))
            .thenReturn(Optional.empty());
        when(verificationRequestRepository.save(any(IdentityVerificationRequest.class)))
            .thenReturn(savedRequest);
        when(gbgGateway.createProfile(any(IdentityVerificationRequest.class)))
            .thenReturn("gbg-profile-123");
        when(verificationMapper.toJsonString(any())).thenReturn("{}");

        // When
        var result = identityVerificationService.initiateUserVerification(userId, request);

        // Then
        assertNotNull(result);
        verify(verificationRequestRepository, times(2)).save(any(IdentityVerificationRequest.class));
        verify(gbgGateway).createProfile(any(IdentityVerificationRequest.class));
        verify(auditLogService).logVerificationInitiated(any(), eq(userId), isNull());
    }

    @Test
    void initiateUserVerification_ExistingActiveVerification() {
        // Given
        Long userId = 12345L;
        InitiateVerificationRequestDto request = InitiateVerificationRequestDto.builder()
            .verificationType(VerificationType.KYC)
            .personalInfo(PersonalInfoDto.builder()
                .firstName("John")
                .lastName("Doe")
                .build())
            .build();

        IdentityVerificationRequest existingRequest = IdentityVerificationRequest.builder()
            .id(UUID.randomUUID())
            .userId(userId)
            .verificationType(VerificationType.KYC)
            .status(VerificationStatus.IN_PROGRESS)
            .build();

        when(verificationRequestRepository.findByUserIdAndStatusIn(eq(userId), any()))
            .thenReturn(Optional.of(existingRequest));

        // When
        var result = identityVerificationService.initiateUserVerification(userId, request);

        // Then
        assertNotNull(result);
        verify(verificationRequestRepository, never()).save(any());
        verify(gbgGateway, never()).createProfile(any());
    }

    @Test
    void initiateUserVerification_InvalidVerificationType() {
        // Given
        Long userId = 12345L;
        InitiateVerificationRequestDto request = InitiateVerificationRequestDto.builder()
            .verificationType(VerificationType.KYB) // Wrong type for user verification
            .build();

        // When & Then
        assertThrows(IllegalArgumentException.class, () -> {
            identityVerificationService.initiateUserVerification(userId, request);
        });
    }

    @Test
    void initiateUserVerification_MissingPersonalInfo() {
        // Given
        Long userId = 12345L;
        InitiateVerificationRequestDto request = InitiateVerificationRequestDto.builder()
            .verificationType(VerificationType.KYC)
            .personalInfo(null) // Missing required personal info
            .build();

        // When & Then
        assertThrows(IllegalArgumentException.class, () -> {
            identityVerificationService.initiateUserVerification(userId, request);
        });
    }

    @Test
    void getUserVerificationStatus_Found() {
        // Given
        Long userId = 12345L;
        IdentityVerificationRequest request = IdentityVerificationRequest.builder()
            .id(UUID.randomUUID())
            .userId(userId)
            .verificationType(VerificationType.KYC)
            .status(VerificationStatus.COMPLETED)
            .riskScore(25)
            .build();

        when(verificationRequestRepository.findTopByUserIdOrderByCreatedAtDesc(userId))
            .thenReturn(Optional.of(request));

        // When
        var result = identityVerificationService.getUserVerificationStatus(userId);

        // Then
        assertTrue(result.isPresent());
        verify(verificationMapper).toVerificationStatusResponseDto(request);
    }

    @Test
    void getUserVerificationStatus_NotFound() {
        // Given
        Long userId = 12345L;
        when(verificationRequestRepository.findTopByUserIdOrderByCreatedAtDesc(userId))
            .thenReturn(Optional.empty());

        // When
        var result = identityVerificationService.getUserVerificationStatus(userId);

        // Then
        assertFalse(result.isPresent());
    }

    @Test
    void submitVerificationRequest_Success() {
        // Given
        UUID verificationId = UUID.randomUUID();
        IdentityVerificationRequest request = IdentityVerificationRequest.builder()
            .id(verificationId)
            .status(VerificationStatus.DOCUMENTS_UPLOADED)
            .build();

        when(verificationRequestRepository.findById(verificationId))
            .thenReturn(Optional.of(request));
        when(verificationRequestRepository.save(any(IdentityVerificationRequest.class)))
            .thenReturn(request);

        // When
        var result = identityVerificationService.submitVerificationRequest(verificationId);

        // Then
        assertNotNull(result);
        verify(gbgGateway).submitVerification(request);
        verify(verificationRequestRepository).save(argThat(req -> 
            req.getStatus() == VerificationStatus.IN_PROGRESS));
        verify(auditLogService).logVerificationSubmitted(request);
    }

    @Test
    void submitVerificationRequest_NotFound() {
        // Given
        UUID verificationId = UUID.randomUUID();
        when(verificationRequestRepository.findById(verificationId))
            .thenReturn(Optional.empty());

        // When & Then
        assertThrows(IllegalArgumentException.class, () -> {
            identityVerificationService.submitVerificationRequest(verificationId);
        });
    }

    @Test
    void submitVerificationRequest_InvalidState() {
        // Given
        UUID verificationId = UUID.randomUUID();
        IdentityVerificationRequest request = IdentityVerificationRequest.builder()
            .id(verificationId)
            .status(VerificationStatus.COMPLETED) // Cannot submit completed verification
            .build();

        when(verificationRequestRepository.findById(verificationId))
            .thenReturn(Optional.of(request));

        // When & Then
        assertThrows(IllegalStateException.class, () -> {
            identityVerificationService.submitVerificationRequest(verificationId);
        });
    }

    @Test
    void updateVerificationStatus_Success() {
        // Given
        UUID verificationId = UUID.randomUUID();
        IdentityVerificationRequest request = IdentityVerificationRequest.builder()
            .id(verificationId)
            .status(VerificationStatus.IN_PROGRESS)
            .build();

        when(verificationRequestRepository.findById(verificationId))
            .thenReturn(Optional.of(request));
        when(verificationRequestRepository.save(any(IdentityVerificationRequest.class)))
            .thenReturn(request);

        // When
        var result = identityVerificationService.updateVerificationStatus(
            verificationId, VerificationStatus.COMPLETED);

        // Then
        assertNotNull(result);
        assertEquals(VerificationStatus.COMPLETED, result.getStatus());
        assertNotNull(result.getCompletedAt());
        verify(auditLogService).logStatusChange(eq(request), 
            eq(VerificationStatus.IN_PROGRESS), eq(VerificationStatus.COMPLETED));
        verify(riskAssessmentService).calculateAndSaveRiskAssessment(request);
    }

    @Test
    void isVerificationExpired_True() {
        // Given
        UUID verificationId = UUID.randomUUID();
        IdentityVerificationRequest request = IdentityVerificationRequest.builder()
            .id(verificationId)
            .status(VerificationStatus.IN_PROGRESS)
            .createdAt(LocalDateTime.now().minusDays(35)) // 35 days ago
            .build();

        when(verificationRequestRepository.findById(verificationId))
            .thenReturn(Optional.of(request));

        // When
        boolean result = identityVerificationService.isVerificationExpired(verificationId);

        // Then
        assertTrue(result);
    }

    @Test
    void isVerificationExpired_False() {
        // Given
        UUID verificationId = UUID.randomUUID();
        IdentityVerificationRequest request = IdentityVerificationRequest.builder()
            .id(verificationId)
            .status(VerificationStatus.IN_PROGRESS)
            .createdAt(LocalDateTime.now().minusDays(15)) // 15 days ago
            .build();

        when(verificationRequestRepository.findById(verificationId))
            .thenReturn(Optional.of(request));

        // When
        boolean result = identityVerificationService.isVerificationExpired(verificationId);

        // Then
        assertFalse(result);
    }
}
