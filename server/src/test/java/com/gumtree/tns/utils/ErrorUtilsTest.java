package com.gumtree.tns.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;

import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import com.gumtree.tns.identityverification.model.ApiError;

class ErrorUtilsTest {

  @Test
  void createUnknownApiErrorsWithGeneratedErrorCode() {
    // given
    HttpStatus httpStatus = HttpStatus.I_AM_A_TEAPOT;

    // when
    ApiError apiError = ErrorUtils.createUnknownApiError(httpStatus);

    // then
    assertEquals(httpStatus.value(), apiError.getStatus());
    assertEquals("unknown-000", apiError.getType());
  }

}
