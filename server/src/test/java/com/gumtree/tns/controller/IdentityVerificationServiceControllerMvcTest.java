package com.gumtree.tns.controller;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.web.servlet.MockMvc;
import com.gumtree.tns.identityverification.controller.IdentityVerificationController;
import com.gumtree.tns.identityverification.service.IdentityVerificationService;
import com.gumtree.tns.identityverification.service.DocumentService;
import com.gumtree.tns.identityverification.service.mapper.VerificationControllerMapper;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@WebMvcTest(IdentityVerificationController.class)
class IdentityVerificationServiceControllerMvcTest {

  @Autowired
  private MockMvc mockMvc;

  @MockBean
  private IdentityVerificationService identityVerificationService;
  
  @MockBean
  private DocumentService documentService;
  
  @MockBean
  private VerificationControllerMapper verificationControllerMapper;
  
  @MockBean
  private MeterRegistry meterRegistry;

  @BeforeEach
  void setUp() {
    // Mock meter registry to return simple counters and timers
    when(meterRegistry.counter(anyString())).thenReturn(new SimpleMeterRegistry().counter("test"));
    when(meterRegistry.timer(anyString())).thenReturn(new SimpleMeterRegistry().timer("test"));
  }

  @Test
  void hello() {

  }

}
