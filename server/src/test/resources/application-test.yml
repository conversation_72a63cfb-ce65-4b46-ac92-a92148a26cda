server:
  port: 0

spring:
  application:
    name: identity-verification-service-test
  
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
    driver-class-name: org.h2.Driver
  
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: false
  
  liquibase:
    enabled: false
  
  jackson:
    serialization:
      write-dates-as-timestamps: false

# Management and monitoring
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always

# Logging configuration
logging:
  level:
    com.gumtree.tns.identityverification: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE

# Application specific configuration
identity-verification:
  gbg:
    base-url: http://localhost:8080/mock-gbg
    api-key: test-api-key
    timeout:
      connect: 1000
      read: 5000
    retry:
      max-attempts: 1
      delay: 100
  
  document:
    upload:
      max-file-size: 1MB
      allowed-types: 
        - image/jpeg
        - image/png
        - application/pdf
      virus-scan:
        enabled: false
  
  verification:
    expiry-days: 30
    risk-threshold:
      low: 30
      medium: 60
      high: 80
  
  gdpr:
    processing:
      timeout: 30000 # 30 seconds
    data-retention:
      audit-logs-days: 365
      verification-data-days: 365
