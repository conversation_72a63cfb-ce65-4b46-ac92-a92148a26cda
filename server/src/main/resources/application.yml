server:
  port: 8080
  servlet:
    context-path: /identity-verification

spring:
  application:
    name: identity-verification-service
  
  datasource:
    url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/${DB_NAME:identity_verification}
    username: ${DB_USERNAME:postgres}
    password: ${DB_PASSWORD:password}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
  
  liquibase:
    change-log: classpath:db/changelog/db.changelog-master.xml
    enabled: true
  
  jackson:
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

# Management and monitoring
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
      base-path: /actuator
  endpoint:
    health:
      show-details: always
      show-components: always
    metrics:
      enabled: true
    prometheus:
      enabled: true
  security:
    enabled: false
  metrics:
    export:
      prometheus:
        enabled: true
    distribution:
      percentiles-histogram:
        http.server.requests: true
        identity.verification.requests.duration: true
        gbg.api.calls.duration: true
        gdpr.processing.duration: true
      percentiles:
        http.server.requests: 0.5, 0.95, 0.99
        identity.verification.requests.duration: 0.5, 0.95, 0.99
        gbg.api.calls.duration: 0.5, 0.95, 0.99
        gdpr.processing.duration: 0.5, 0.95, 0.99

# Logging configuration
logging:
  level:
    com.gumtree.tns.identityverification: INFO
    org.springframework.web: INFO
    org.hibernate.SQL: WARN
    org.hibernate.type.descriptor.sql.BasicBinder: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Application specific configuration
identity-verification:
  gbg:
    base-url: ${GBG_BASE_URL:https://api.gbg.com}
    api-key: ${GBG_API_KEY:your-api-key}
    timeout:
      connect: 5000
      read: 30000
    retry:
      max-attempts: 3
      delay: 1000
  
  document:
    upload:
      max-file-size: 10MB
      allowed-types: 
        - image/jpeg
        - image/png
        - application/pdf
      virus-scan:
        enabled: true
  
  verification:
    expiry-days: 30
    risk-threshold:
      low: 30
      medium: 60
      high: 80
  
  gdpr:
    processing:
      timeout: 300000 # 5 minutes
    data-retention:
      audit-logs-days: 2555 # 7 years
      verification-data-days: 2555 # 7 years

# Security configuration
security:
  require-ssl: false
  cors:
    allowed-origins: "*"
    allowed-methods: GET,POST,PUT,DELETE,OPTIONS
    allowed-headers: "*"
    allow-credentials: false
