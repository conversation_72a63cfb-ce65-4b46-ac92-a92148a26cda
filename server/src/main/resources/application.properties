server.port=${PORT:8080}
management.endpoints.jmx.exposure.include=*
management.metrics.export.jmx.domain=com.gumtree.metrics
management.endpoints.web.exposure.include=health,info,prometheus
management.endpoints.web.base-path=/internal
management.endpoints.web.path-mapping.prometheus=metrics
# To send 100% of traces to Stackdriver Trace
spring.sleuth.sampler.probability=1.0
# To ignore some frequently used URL patterns that are not useful in trace
spring.sleuth.web.skipPattern=(^cleanup.*|.+favicon.*)

management.metrics.distribution.percentiles.all=0.50, 0.90, 0.99
management.metrics.distribution.sla.http.server.requests=1ms,5ms

debug.stackdriver.enabled=${DEBUG_STACKDRIVER_ENABLED:false}
spring.cloud.gcp.logging.enabled=${debug.stackdriver.enabled}
# Spring Sleuth is used for tracing..
spring.cloud.gcp.trace.enabled=false
spring.cloud.gcp.core.enabled=${debug.stackdriver.enabled}
# Disable OSIV
spring.jpa.open-in-view=false
