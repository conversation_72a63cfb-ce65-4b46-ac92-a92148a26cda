package com.gumtree.exception;

import java.io.Serial;
import com.gumtree.tns.identityverification.model.ApiError;

/**
 * Base exception class. Every custom exception has too extend this class.
 */
public class BaseException extends RuntimeException {

  @Serial
  private static final long serialVersionUID = -821834626317313276L;

  // Encapsulated error data object
  private ApiError apiError;

  public BaseException(ApiError apiError) {
    super(apiError.getDetail());
    this.apiError = apiError;
  }

  public BaseException(ApiError apiError, Throwable cause) {
    super(apiError.getDetail(), cause);
    this.apiError = apiError;
  }

  public ApiError getApiError() {
    return apiError;
  }

}
