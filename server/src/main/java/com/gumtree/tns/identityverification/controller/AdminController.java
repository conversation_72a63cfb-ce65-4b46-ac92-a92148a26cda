package com.gumtree.tns.identityverification.controller;

import com.gumtree.tns.identityverification.service.AdminApi;
import com.gumtree.tns.identityverification.model.VerificationRequestsPageResponse;
import com.gumtree.tns.identityverification.model.VerificationStatus;
import com.gumtree.tns.identityverification.service.IdentityVerificationService;
import com.gumtree.tns.identityverification.service.mapper.VerificationControllerMapper;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.RequiredArgsConstructor;
import lombok.extern.flogger.Flogger;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * REST Controller for administrative operations
 */
@Flogger
@RestController
@RequiredArgsConstructor
public class AdminController implements AdminApi {

    private final IdentityVerificationService identityVerificationService;
    private final VerificationControllerMapper controllerMapper;
    private final MeterRegistry meterRegistry;

    // Metrics
    private final Counter adminRequestsCounter;
    private final Timer adminRequestsTimer;

    public AdminController(
            IdentityVerificationService identityVerificationService,
            VerificationControllerMapper controllerMapper,
            MeterRegistry meterRegistry) {
        this.identityVerificationService = identityVerificationService;
        this.controllerMapper = controllerMapper;
        this.meterRegistry = meterRegistry;
        
        // Initialize metrics
        this.adminRequestsCounter = Counter.builder("admin.requests.total")
            .description("Total number of admin requests")
            .register(meterRegistry);
        
        this.adminRequestsTimer = Timer.builder("admin.requests.duration")
            .description("Duration of admin requests")
            .register(meterRegistry);
    }

    @Override
    public ResponseEntity<VerificationRequestsPageResponse> getVerificationRequests(
            @Valid @RequestParam(required = false) VerificationStatus status,
            @Valid @RequestParam(name = "user-id", required = false) Long userId,
            @Valid @RequestParam(name = "account-id", required = false) Long accountId,
            @Valid @RequestParam(defaultValue = "0") Integer page,
            @Valid @RequestParam(defaultValue = "20") Integer size) {
        
        log.atInfo().log("Getting verification requests for admin review - status: %s, userId: %s, accountId: %s, page: %d, size: %d", 
            status, userId, accountId, page, size);
        
        try {
            return adminRequestsTimer.recordCallable(() -> {
            try {
                // TODO: Fix counter increment call
                // adminRequestsCounter.increment("operation", "get_verification_requests");
                
                Pageable pageable = PageRequest.of(page, size);
                var statusEnum = status != null ? 
                    com.gumtree.tns.identityverification.dao.model.VerificationStatus.valueOf(status.name()) : null;
                
                var verificationRequestsPage = identityVerificationService.getVerificationRequestsForReview(
                    statusEnum, userId, accountId, pageable);
                
                var response = controllerMapper.toVerificationRequestsPageResponse(verificationRequestsPage);
                
                log.atInfo().log("Retrieved %d verification requests for admin review", 
                    response.getTotalElements());
                
                return ResponseEntity.ok(response);
                
            } catch (Exception e) {
                log.atSevere().withCause(e).log("Failed to get verification requests for admin review");
                throw e;
            }
        });
        } catch (Exception e) {
            log.atSevere().withCause(e).log("Failed to record timer for admin verification requests");
            throw new RuntimeException("Failed to get verification requests for admin review", e);
        }
    }
}
