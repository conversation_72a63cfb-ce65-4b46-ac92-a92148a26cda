package com.gumtree.tns.identityverification.dto;

import com.gumtree.tns.identityverification.dao.model.VerificationStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * DTO for verification response
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VerificationResponseDto {

    private UUID verificationId;

    private VerificationStatus status;

    private String gbgProfileId;

    private LocalDateTime createdAt;

    private LocalDateTime estimatedCompletionTime;
}
