package com.gumtree.tns.identityverification.dao.repository;

import com.gumtree.tns.identityverification.dao.model.VerificationDocument;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository interface for VerificationDocument entity
 */
@Repository
public interface VerificationDocumentRepository extends JpaRepository<VerificationDocument, UUID> {

    /**
     * Find verification documents by verification request ID
     */
    List<VerificationDocument> findByVerificationRequestId(UUID verificationRequestId);

    /**
     * Find verification document by verification request ID and document type
     */
    Optional<VerificationDocument> findByVerificationRequestIdAndDocumentType(
        UUID verificationRequestId, VerificationDocument.DocumentType documentType);

    /**
     * Find verification documents by document status
     */
    List<VerificationDocument> findByDocumentStatus(VerificationDocument.DocumentStatus documentStatus);

    /**
     * Find verification document by GBG document ID
     */
    Optional<VerificationDocument> findByGbgDocumentId(String gbgDocumentId);

    /**
     * Delete verification documents by verification request ID
     */
    void deleteByVerificationRequestId(UUID verificationRequestId);

    /**
     * Count documents by verification request ID
     */
    long countByVerificationRequestId(UUID verificationRequestId);

    /**
     * Count documents by verification request ID and status
     */
    long countByVerificationRequestIdAndDocumentStatus(
        UUID verificationRequestId, VerificationDocument.DocumentStatus documentStatus);
}
