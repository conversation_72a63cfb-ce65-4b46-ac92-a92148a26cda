package com.gumtree.tns.identityverification.exception;

/**
 * Exception thrown when a verification request is not found
 */
public class VerificationNotFoundException extends RuntimeException {

    public VerificationNotFoundException(String message) {
        super(message);
    }

    public VerificationNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }

    public static VerificationNotFoundException forId(String verificationId) {
        return new VerificationNotFoundException("Verification request not found with ID: " + verificationId);
    }

    public static VerificationNotFoundException forUserId(Long userId) {
        return new VerificationNotFoundException("No verification request found for user ID: " + userId);
    }

    public static VerificationNotFoundException forAccountId(Long accountId) {
        return new VerificationNotFoundException("No verification request found for account ID: " + accountId);
    }
}
