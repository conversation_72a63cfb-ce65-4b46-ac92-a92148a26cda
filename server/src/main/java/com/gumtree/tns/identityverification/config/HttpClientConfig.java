package com.gumtree.tns.identityverification.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

/**
 * Configuration for HTTP clients
 */
@Configuration
public class HttpClientConfig {

    @Value("${identity-verification.gbg.timeout.connect:5000}")
    private int connectTimeout;
    
    @Value("${identity-verification.gbg.timeout.read:30000}")
    private int readTimeout;

    /**
     * Creates a RestTemplate bean for HTTP client operations
     */
    @Bean
    public RestTemplate restTemplate() {
        RestTemplate restTemplate = new RestTemplate();
        restTemplate.setRequestFactory(clientHttpRequestFactory());
        return restTemplate;
    }

    /**
     * Creates a ClientHttpRequestFactory with configured timeouts
     */
    @Bean
    public ClientHttpRequestFactory clientHttpRequestFactory() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(connectTimeout);
        factory.setReadTimeout(readTimeout);
        return factory;
    }
}