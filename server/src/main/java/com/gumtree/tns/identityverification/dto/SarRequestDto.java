package com.gumtree.tns.identityverification.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;

/**
 * DTO for Subject Access Request
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SarRequestDto {

    @NotBlank
    private String requestId;

    @NotBlank
    private String subtaskId;

    @NotBlank
    @Email
    private String email;

    private String firstName;

    private String lastName;

    private String phoneNumber;
}
