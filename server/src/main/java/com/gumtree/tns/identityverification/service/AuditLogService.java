package com.gumtree.tns.identityverification.service;

import com.gumtree.tns.identityverification.dao.model.AuditLog;
import com.gumtree.tns.identityverification.dao.model.IdentityVerificationRequest;
import com.gumtree.tns.identityverification.dao.model.VerificationDocument;
import com.gumtree.tns.identityverification.dao.model.VerificationStatus;

import java.util.List;
import java.util.UUID;

/**
 * Service interface for audit logging operations
 */
public interface AuditLogService {

    /**
     * Log verification initiated event
     *
     * @param verificationRequest the verification request
     * @param userId the user ID (if applicable)
     * @param accountId the account ID (if applicable)
     */
    void logVerificationInitiated(IdentityVerificationRequest verificationRequest, Long userId, Long accountId);

    /**
     * Log verification submitted event
     *
     * @param verificationRequest the verification request
     */
    void logVerificationSubmitted(IdentityVerificationRequest verificationRequest);

    /**
     * Log status change event
     *
     * @param verificationRequest the verification request
     * @param oldStatus the old status
     * @param newStatus the new status
     */
    void logStatusChange(IdentityVerificationRequest verificationRequest, VerificationStatus oldStatus, VerificationStatus newStatus);

    /**
     * Log document uploaded event
     *
     * @param document the uploaded document
     */
    void logDocumentUploaded(VerificationDocument document);

    /**
     * Log document deleted event
     *
     * @param document the deleted document
     */
    void logDocumentDeleted(VerificationDocument document);

    /**
     * Log document status changed event
     *
     * @param document the document
     * @param oldStatus the old status
     * @param newStatus the new status
     */
    void logDocumentStatusChanged(VerificationDocument document, 
                                 VerificationDocument.DocumentStatus oldStatus, 
                                 VerificationDocument.DocumentStatus newStatus);

    /**
     * Log SAR processed event
     *
     * @param requestId the SAR request ID
     * @param email the user email
     * @param segmentCount the number of data segments returned
     */
    void logSarProcessed(String requestId, String email, int segmentCount);

    /**
     * Log DDR processed event
     *
     * @param requestId the DDR request ID
     * @param email the user email
     * @param deletedRecords the number of deleted records
     */
    void logDdrProcessed(String requestId, String email, int deletedRecords);

    /**
     * Log webhook event
     *
     * @param eventType the event type
     * @param eventData the event data
     */
    void logWebhookEvent(String eventType, String eventData);

    /**
     * Get audit logs for verification request
     *
     * @param verificationId the verification request ID
     * @return list of audit logs
     */
    List<AuditLog> getAuditLogsForVerification(UUID verificationId);

    /**
     * Get audit logs for user
     *
     * @param userId the user ID
     * @return list of audit logs
     */
    List<AuditLog> getAuditLogsForUser(Long userId);

    /**
     * Get audit logs for account
     *
     * @param accountId the account ID
     * @return list of audit logs
     */
    List<AuditLog> getAuditLogsForAccount(Long accountId);

    /**
     * Clean up old audit logs
     *
     * @param retentionDays the number of days to retain logs
     * @return number of deleted logs
     */
    long cleanupOldAuditLogs(int retentionDays);
}
