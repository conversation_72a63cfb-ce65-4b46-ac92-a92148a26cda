package com.gumtree.tns.identityverification.dto;

import com.gumtree.tns.identityverification.dao.model.RepresentativeType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * DTO for representative information
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RepresentativeInfoDto {

    @NotNull
    private RepresentativeType type;

    @NotNull
    private PersonalInfoDto personalInfo;

    private String position;

    private String shareholding;

    private Boolean isSignatory;

    private Boolean isPep; // Politically Exposed Person

    private String relationshipToCompany;
}
