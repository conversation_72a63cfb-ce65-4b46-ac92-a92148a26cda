package com.gumtree.tns.identityverification.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for Data Deletion Response
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DdrResponseDto {

    private String requestId;

    private String subtaskId;

    private boolean error;

    private String reason;

    private int deletedRecords;

    // Manual builder method for compilation compatibility
    public static DdrResponseDtoBuilder builder() {
        return new DdrResponseDtoBuilder();
    }

    public static class DdrResponseDtoBuilder {
        private String requestId;
        private String subtaskId;
        private boolean error;
        private String reason;
        private int deletedRecords;

        public DdrResponseDtoBuilder requestId(String requestId) {
            this.requestId = requestId;
            return this;
        }

        public DdrResponseDtoBuilder subtaskId(String subtaskId) {
            this.subtaskId = subtaskId;
            return this;
        }

        public DdrResponseDtoBuilder error(boolean error) {
            this.error = error;
            return this;
        }

        public DdrResponseDtoBuilder reason(String reason) {
            this.reason = reason;
            return this;
        }

        public DdrResponseDtoBuilder deletedRecords(int deletedRecords) {
            this.deletedRecords = deletedRecords;
            return this;
        }

        public DdrResponseDto build() {
            return new DdrResponseDto(requestId, subtaskId, error, reason, deletedRecords);
        }
    }
}
