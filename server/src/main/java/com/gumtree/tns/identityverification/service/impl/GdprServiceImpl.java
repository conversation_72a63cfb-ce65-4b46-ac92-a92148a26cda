package com.gumtree.tns.identityverification.service.impl;

import com.gumtree.tns.identityverification.dao.model.IdentityVerificationRequest;
import com.gumtree.tns.identityverification.dao.repository.IdentityVerificationRequestRepository;
import com.gumtree.tns.identityverification.dao.repository.AuditLogRepository;
import com.gumtree.tns.identityverification.dao.repository.VerificationDocumentRepository;
import com.gumtree.tns.identityverification.dao.repository.RepresentativeRepository;
import com.gumtree.tns.identityverification.dto.SarRequestDto;
import com.gumtree.tns.identityverification.dto.SarResponseDto;
import com.gumtree.tns.identityverification.dto.DdrRequestDto;
import com.gumtree.tns.identityverification.dto.DdrResponseDto;
import com.gumtree.tns.identityverification.dto.PersonalDataSegmentDto;
import com.gumtree.tns.identityverification.service.AuditLogService;
import com.gumtree.tns.identityverification.service.GdprService;
import com.gumtree.tns.identityverification.service.UserLookupService;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.RequiredArgsConstructor;
import lombok.extern.flogger.Flogger;
import org.springframework.stereotype.Service;
import com.google.common.flogger.FluentLogger;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Implementation of GdprService
 */
@Flogger
@Service
@RequiredArgsConstructor
public class GdprServiceImpl implements GdprService {

    private static final FluentLogger log = FluentLogger.forEnclosingClass();

    private final IdentityVerificationRequestRepository verificationRequestRepository;
    private final AuditLogRepository auditLogRepository;
    private final VerificationDocumentRepository documentRepository;
    private final RepresentativeRepository representativeRepository;
    private final UserLookupService userLookupService;
    private final AuditLogService auditLogService;
    private final ObjectMapper objectMapper;
    private final MeterRegistry meterRegistry;

    // Metrics
    private final Counter sarProcessingCounter;
    private final Counter ddrProcessingCounter;
    private final Timer gdprProcessingTimer;

    public GdprServiceImpl(
            IdentityVerificationRequestRepository verificationRequestRepository,
            AuditLogRepository auditLogRepository,
            VerificationDocumentRepository documentRepository,
            RepresentativeRepository representativeRepository,
            UserLookupService userLookupService,
            AuditLogService auditLogService,
            ObjectMapper objectMapper,
            MeterRegistry meterRegistry) {
        this.verificationRequestRepository = verificationRequestRepository;
        this.auditLogRepository = auditLogRepository;
        this.documentRepository = documentRepository;
        this.representativeRepository = representativeRepository;
        this.userLookupService = userLookupService;
        this.auditLogService = auditLogService;
        this.objectMapper = objectMapper;
        this.meterRegistry = meterRegistry;
        
        // Initialize metrics with specific tags
        this.sarProcessingCounter = Counter.builder("gdpr.processing.total")
            .description("Total number of SAR processing requests")
            .tag("type", "sar")
            .register(meterRegistry);
            
        this.ddrProcessingCounter = Counter.builder("gdpr.processing.total")
            .description("Total number of DDR processing requests")
            .tag("type", "ddr")
            .register(meterRegistry);
        
        this.gdprProcessingTimer = Timer.builder("gdpr.processing.duration")
            .description("Duration of GDPR processing requests")
            .register(meterRegistry);
    }

    @Override
    @Transactional(readOnly = true)
    public SarResponseDto processSubjectAccessRequest(SarRequestDto sarRequest) {
        log.atInfo().log("Processing Subject Access Request for email: %s, requestId: %s", 
            sarRequest.getEmail(), sarRequest.getRequestId());
        
        try {
            return gdprProcessingTimer.recordCallable(() -> {
            try {
                // TODO: Fix counter increment call
                // sarProcessingCounter.increment();
                
                // Validate request
                if (!validateGdprRequest(sarRequest.getRequestId(), sarRequest.getEmail())) {
                    return createSarErrorResponse(sarRequest, "Invalid GDPR request");
                }
                
                // Look up user by email
                var userIds = userLookupService.findUserIdsByEmail(sarRequest.getEmail());
                var accountIds = userLookupService.findAccountIdsByEmail(sarRequest.getEmail());
                
                if (userIds.isEmpty() && accountIds.isEmpty()) {
                    log.atInfo().log("No user data found for email: %s", sarRequest.getEmail());
                    return createSarSuccessResponse(sarRequest, List.of());
                }
                
                // Collect personal data segments
                List<PersonalDataSegmentDto> personalDataSegments = new ArrayList<>();
                
                // Collect verification request data
                if (!userIds.isEmpty()) {
                    personalDataSegments.addAll(collectUserVerificationData(userIds));
                }
                
                if (!accountIds.isEmpty()) {
                    personalDataSegments.addAll(collectAccountVerificationData(accountIds));
                }
                
                // Collect audit log data
                personalDataSegments.addAll(collectAuditLogData(userIds, accountIds));
                
                // Log SAR processing
                auditLogService.logSarProcessed(sarRequest.getRequestId(), sarRequest.getEmail(), 
                    personalDataSegments.size());
                
                log.atInfo().log("Subject Access Request processed successfully for email: %s, segments: %d", 
                    sarRequest.getEmail(), personalDataSegments.size());
                
                return createSarSuccessResponse(sarRequest, personalDataSegments);
                
            } catch (Exception e) {
                log.atSevere().withCause(e).log("Failed to process Subject Access Request for email: %s", 
                    sarRequest.getEmail());
                return createSarErrorResponse(sarRequest, "Failed to process request: " + e.getMessage());
            }
        });
        } catch (Exception e) {
            log.atSevere().withCause(e).log("Failed to process Subject Access Request for email: %s", 
                sarRequest.getEmail());
            return createSarErrorResponse(sarRequest, "Failed to process request: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public DdrResponseDto processDataDeletionRequest(DdrRequestDto ddrRequest) {
        log.atInfo().log("Processing Data Deletion Request for email: %s, requestId: %s", 
            ddrRequest.getEmail(), ddrRequest.getRequestId());
        
        try {
            return gdprProcessingTimer.recordCallable(() -> {
            try {
                // TODO: Fix counter increment call
                // ddrProcessingCounter.increment();
                
                // Validate request
                if (!validateGdprRequest(ddrRequest.getRequestId(), ddrRequest.getEmail())) {
                    return createDdrErrorResponse(ddrRequest, "Invalid GDPR request");
                }
                
                // Look up user by email
                var userIds = userLookupService.findUserIdsByEmail(ddrRequest.getEmail());
                var accountIds = userLookupService.findAccountIdsByEmail(ddrRequest.getEmail());
                
                if (userIds.isEmpty() && accountIds.isEmpty()) {
                    log.atInfo().log("No user data found for deletion for email: %s", ddrRequest.getEmail());
                    return createDdrSuccessResponse(ddrRequest);
                }
                
                int deletedRecords = 0;
                
                // Delete verification request data
                if (!userIds.isEmpty()) {
                    deletedRecords += deleteUserVerificationData(userIds);
                }
                
                if (!accountIds.isEmpty()) {
                    deletedRecords += deleteAccountVerificationData(accountIds);
                }
                
                // Anonymize audit logs (cannot delete for compliance reasons)
                anonymizeAuditLogData(userIds, accountIds);
                
                // Log DDR processing
                auditLogService.logDdrProcessed(ddrRequest.getRequestId(), ddrRequest.getEmail(), deletedRecords);
                
                log.atInfo().log("Data Deletion Request processed successfully for email: %s, deleted records: %d", 
                    ddrRequest.getEmail(), deletedRecords);
                
                return createDdrSuccessResponse(ddrRequest);
                
            } catch (Exception e) {
                log.atSevere().withCause(e).log("Failed to process Data Deletion Request for email: %s", 
                    ddrRequest.getEmail());
                return createDdrErrorResponse(ddrRequest, "Failed to process request: " + e.getMessage());
            }
        });
        } catch (Exception e) {
            log.atSevere().withCause(e).log("Failed to process Data Deletion Request for email: %s", 
                ddrRequest.getEmail());
            return createDdrErrorResponse(ddrRequest, "Failed to process request: " + e.getMessage());
        }
    }

    @Override
    @Transactional(readOnly = true)
    public boolean hasUserVerificationData(String email) {
        var userIds = userLookupService.findUserIdsByEmail(email);
        var accountIds = userLookupService.findAccountIdsByEmail(email);
        
        if (userIds.isEmpty() && accountIds.isEmpty()) {
            return false;
        }
        
        // Check for verification requests
        for (Long userId : userIds) {
            if (verificationRequestRepository.findTopByUserIdOrderByCreatedAtDesc(userId).isPresent()) {
                return true;
            }
        }
        
        for (Long accountId : accountIds) {
            if (verificationRequestRepository.findTopByAccountIdOrderByCreatedAtDesc(accountId).isPresent()) {
                return true;
            }
        }
        
        return false;
    }

    @Override
    @Transactional(readOnly = true)
    public long getUserVerificationDataCount(String email) {
        var userIds = userLookupService.findUserIdsByEmail(email);
        var accountIds = userLookupService.findAccountIdsByEmail(email);
        
        long count = 0;
        
        // Count verification requests
        for (Long userId : userIds) {
            count += verificationRequestRepository.countByUserId(userId);
        }
        
        for (Long accountId : accountIds) {
            count += verificationRequestRepository.countByAccountId(accountId);
        }
        
        return count;
    }

    @Override
    @Transactional
    public boolean anonymizeUserData(String email) {
        log.atInfo().log("Anonymizing user data for email: %s", email);

        try {
            var userIds = userLookupService.findUserIdsByEmail(email);
            var accountIds = userLookupService.findAccountIdsByEmail(email);

            // Anonymize verification requests
            for (Long userId : userIds) {
                var verificationRequests = verificationRequestRepository.findByUserId(userId);
                for (var request : verificationRequests) {
                    anonymizeVerificationRequest(request);
                }
            }

            for (Long accountId : accountIds) {
                var verificationRequests = verificationRequestRepository.findByAccountId(accountId);
                for (var request : verificationRequests) {
                    anonymizeVerificationRequest(request);
                }
            }

            // Anonymize audit logs
            anonymizeAuditLogData(userIds, accountIds);

            log.atInfo().log("User data anonymized successfully for email: %s", email);
            return true;

        } catch (Exception e) {
            log.atSevere().withCause(e).log("Failed to anonymize user data for email: %s", email);
            return false;
        }
    }

    @Override
    @Transactional(readOnly = true)
    public String exportUserDataAsJson(String email) {
        log.atInfo().log("Exporting user data as JSON for email: %s", email);

        try {
            var userIds = userLookupService.findUserIdsByEmail(email);
            var accountIds = userLookupService.findAccountIdsByEmail(email);

            Map<String, Object> userData = new HashMap<>();
            userData.put("email", email);
            userData.put("exportDate", LocalDateTime.now());
            userData.put("userIds", userIds);
            userData.put("accountIds", accountIds);

            // Export verification data
            List<Map<String, Object>> verificationData = new ArrayList<>();

            for (Long userId : userIds) {
                var requests = verificationRequestRepository.findByUserId(userId);
                for (var request : requests) {
                    verificationData.add(convertVerificationRequestToMap(request));
                }
            }

            for (Long accountId : accountIds) {
                var requests = verificationRequestRepository.findByAccountId(accountId);
                for (var request : requests) {
                    verificationData.add(convertVerificationRequestToMap(request));
                }
            }

            userData.put("verificationRequests", verificationData);

            return objectMapper.writeValueAsString(userData);

        } catch (Exception e) {
            log.atSevere().withCause(e).log("Failed to export user data as JSON for email: %s", email);
            return "{}";
        }
    }

    @Override
    public boolean validateGdprRequest(String requestId, String email) {
        // Basic validation - in real implementation, this would include more sophisticated validation
        return requestId != null && !requestId.trim().isEmpty() &&
               email != null && !email.trim().isEmpty() && email.contains("@");
    }

    // Helper methods
    private SarResponseDto createSarSuccessResponse(SarRequestDto request, List<PersonalDataSegmentDto> segments) {
        return SarResponseDto.builder()
            .requestId(request.getRequestId())
            .subtaskId(request.getSubtaskId())
            .error(false)
            .salutation("Dear Customer")
            .personalDataSegments(segments)
            .build();
    }

    private SarResponseDto createSarErrorResponse(SarRequestDto request, String reason) {
        return SarResponseDto.builder()
            .requestId(request.getRequestId())
            .subtaskId(request.getSubtaskId())
            .error(true)
            .reason(reason)
            .build();
    }

    private DdrResponseDto createDdrSuccessResponse(DdrRequestDto request) {
        return DdrResponseDto.builder()
            .requestId(request.getRequestId())
            .subtaskId(request.getSubtaskId())
            .error(false)
            .build();
    }

    private DdrResponseDto createDdrErrorResponse(DdrRequestDto request, String reason) {
        return DdrResponseDto.builder()
            .requestId(request.getRequestId())
            .subtaskId(request.getSubtaskId())
            .error(true)
            .reason(reason)
            .build();
    }

    private List<PersonalDataSegmentDto> collectUserVerificationData(List<Long> userIds) {
        List<PersonalDataSegmentDto> segments = new ArrayList<>();

        for (Long userId : userIds) {
            var verificationRequests = verificationRequestRepository.findByUserId(userId);

            if (!verificationRequests.isEmpty()) {
                List<Map<String, String>> values = new ArrayList<>();

                for (var request : verificationRequests) {
                    Map<String, String> requestData = new HashMap<>();
                    requestData.put("Verification ID", request.getId().toString());
                    requestData.put("Type", request.getVerificationType().toString());
                    requestData.put("Status", request.getStatus().toString());
                    requestData.put("Created At", request.getCreatedAt().toString());
                    requestData.put("Risk Score", request.getRiskScore() != null ? request.getRiskScore().toString() : "N/A");
                    values.add(requestData);
                }

                segments.add(PersonalDataSegmentDto.builder()
                    .title("Identity Verification Records")
                    .description("Records of identity verification attempts and results")
                    .values(values)
                    .build());
            }
        }

        return segments;
    }

    private List<PersonalDataSegmentDto> collectAccountVerificationData(List<Long> accountIds) {
        List<PersonalDataSegmentDto> segments = new ArrayList<>();

        for (Long accountId : accountIds) {
            var verificationRequests = verificationRequestRepository.findByAccountId(accountId);

            if (!verificationRequests.isEmpty()) {
                List<Map<String, String>> values = new ArrayList<>();

                for (var request : verificationRequests) {
                    Map<String, String> requestData = new HashMap<>();
                    requestData.put("Verification ID", request.getId().toString());
                    requestData.put("Type", request.getVerificationType().toString());
                    requestData.put("Status", request.getStatus().toString());
                    requestData.put("Created At", request.getCreatedAt().toString());
                    requestData.put("Risk Score", request.getRiskScore() != null ? request.getRiskScore().toString() : "N/A");
                    values.add(requestData);
                }

                segments.add(PersonalDataSegmentDto.builder()
                    .title("Business Verification Records")
                    .description("Records of business verification attempts and results")
                    .values(values)
                    .build());
            }
        }

        return segments;
    }

    private List<PersonalDataSegmentDto> collectAuditLogData(List<Long> userIds, List<Long> accountIds) {
        List<PersonalDataSegmentDto> segments = new ArrayList<>();
        List<Map<String, String>> values = new ArrayList<>();

        // Collect audit logs for users
        for (Long userId : userIds) {
            var auditLogs = auditLogRepository.findByUserId(userId);
            for (var log : auditLogs) {
                Map<String, String> logData = new HashMap<>();
                logData.put("Action", log.getActionType());
                logData.put("Timestamp", log.getCreatedAt().toString());
                logData.put("IP Address", log.getIpAddress() != null ? log.getIpAddress().toString() : "N/A");
                values.add(logData);
            }
        }

        // Collect audit logs for accounts
        for (Long accountId : accountIds) {
            var auditLogs = auditLogRepository.findByAccountId(accountId);
            for (var log : auditLogs) {
                Map<String, String> logData = new HashMap<>();
                logData.put("Action", log.getActionType());
                logData.put("Timestamp", log.getCreatedAt().toString());
                logData.put("IP Address", log.getIpAddress() != null ? log.getIpAddress().toString() : "N/A");
                values.add(logData);
            }
        }

        if (!values.isEmpty()) {
            segments.add(PersonalDataSegmentDto.builder()
                .title("Audit Logs")
                .description("System audit logs related to identity verification activities")
                .values(values)
                .build());
        }

        return segments;
    }

    private int deleteUserVerificationData(List<Long> userIds) {
        int deletedCount = 0;

        for (Long userId : userIds) {
            var verificationRequests = verificationRequestRepository.findByUserId(userId);

            for (var request : verificationRequests) {
                // Delete related documents
                documentRepository.deleteByVerificationRequestId(request.getId());

                // Delete related representatives
                representativeRepository.deleteByVerificationRequestId(request.getId());

                // Delete verification request
                verificationRequestRepository.delete(request);
                deletedCount++;
            }
        }

        return deletedCount;
    }

    private int deleteAccountVerificationData(List<Long> accountIds) {
        int deletedCount = 0;

        for (Long accountId : accountIds) {
            var verificationRequests = verificationRequestRepository.findByAccountId(accountId);

            for (var request : verificationRequests) {
                // Delete related documents
                documentRepository.deleteByVerificationRequestId(request.getId());

                // Delete related representatives
                representativeRepository.deleteByVerificationRequestId(request.getId());

                // Delete verification request
                verificationRequestRepository.delete(request);
                deletedCount++;
            }
        }

        return deletedCount;
    }

    private void anonymizeAuditLogData(List<Long> userIds, List<Long> accountIds) {
        // Anonymize audit logs by removing personal identifiers
        for (Long userId : userIds) {
            var auditLogs = auditLogRepository.findByUserId(userId);
            for (var log : auditLogs) {
                log.setPerformedBy("ANONYMIZED");
                log.setIpAddress(null);
                log.setUserAgent("ANONYMIZED");
                auditLogRepository.save(log);
            }
        }

        for (Long accountId : accountIds) {
            var auditLogs = auditLogRepository.findByAccountId(accountId);
            for (var log : auditLogs) {
                log.setPerformedBy("ANONYMIZED");
                log.setIpAddress(null);
                log.setUserAgent("ANONYMIZED");
                auditLogRepository.save(log);
            }
        }
    }

    private void anonymizeVerificationRequest(IdentityVerificationRequest request) {
        // Anonymize personal information in verification request
        request.setPersonalInfo(null);
        request.setBusinessInfo(null);
        request.setCustomerReference("ANONYMIZED");
        verificationRequestRepository.save(request);
    }

    private Map<String, Object> convertVerificationRequestToMap(IdentityVerificationRequest request) {
        Map<String, Object> data = new HashMap<>();
        data.put("id", request.getId().toString());
        data.put("verificationType", request.getVerificationType().toString());
        data.put("status", request.getStatus().toString());
        data.put("createdAt", request.getCreatedAt().toString());
        data.put("riskScore", request.getRiskScore());
        data.put("riskLevel", request.getRiskLevel() != null ? request.getRiskLevel().toString() : null);
        return data;
    }

}
