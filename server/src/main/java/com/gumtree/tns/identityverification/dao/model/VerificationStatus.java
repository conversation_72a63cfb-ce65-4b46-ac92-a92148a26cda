package com.gumtree.tns.identityverification.dao.model;

/**
 * Enumeration for verification status
 */
public enum VerificationStatus {
    /**
     * Verification request has been initiated
     */
    INITIATED,
    
    /**
     * Documents are required to proceed
     */
    DOCUMENTS_REQUIRED,
    
    /**
     * Documents have been uploaded
     */
    DOCUMENTS_UPLOADED,
    
    /**
     * Verification is in progress
     */
    IN_PROGRESS,
    
    /**
     * Verification is under manual review
     */
    UNDER_REVIEW,
    
    /**
     * Verification process completed
     */
    COMPLETED,
    
    /**
     * Verification approved
     */
    APPROVED,
    
    /**
     * Verification rejected
     */
    REJECTED,
    
    /**
     * Verification request expired
     */
    EXPIRED
}
