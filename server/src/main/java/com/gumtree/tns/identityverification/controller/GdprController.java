package com.gumtree.tns.identityverification.controller;

import com.gumtree.tns.identityverification.service.GdprApi;
import com.gumtree.tns.identityverification.model.SarRequest;
import com.gumtree.tns.identityverification.model.SarResponse;
import com.gumtree.tns.identityverification.model.DdrRequest;
import com.gumtree.tns.identityverification.model.DdrResponse;
import com.gumtree.tns.identityverification.service.GdprService;
import com.gumtree.tns.identityverification.service.mapper.GdprControllerMapper;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.RequiredArgsConstructor;
import lombok.extern.flogger.Flogger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * REST Controller for GDPR compliance operations
 */
@Flogger
@RestController
@RequiredArgsConstructor
public class GdprController implements GdprApi {

    private final GdprService gdprService;
    private final GdprControllerMapper controllerMapper;
    private final MeterRegistry meterRegistry;

    // Metrics
    private final Counter gdprRequestsCounter;
    private final Timer gdprRequestsTimer;

    public GdprController(
            GdprService gdprService,
            GdprControllerMapper controllerMapper,
            MeterRegistry meterRegistry) {
        this.gdprService = gdprService;
        this.controllerMapper = controllerMapper;
        this.meterRegistry = meterRegistry;
        
        // Initialize metrics
        this.gdprRequestsCounter = Counter.builder("gdpr.requests.total")
            .description("Total number of GDPR requests")
            .register(meterRegistry);
        
        this.gdprRequestsTimer = Timer.builder("gdpr.requests.duration")
            .description("Duration of GDPR requests")
            .register(meterRegistry);
    }

    @Override
    public ResponseEntity<SarResponse> getUserData(@Valid @RequestBody SarRequest sarRequest) {
        log.atInfo().log("Processing Subject Access Request for email: %s, requestId: %s",
            sarRequest.getEmail(), sarRequest.getRequestId());

        try {
            return gdprRequestsTimer.recordCallable(() -> {
            try {
                Counter.builder("gdpr.requests.total")
                    .tag("type", "sar")
                    .register(meterRegistry)
                    .increment();
                
                var requestDto = controllerMapper.toSarRequestDto(sarRequest);
                var responseDto = gdprService.processSubjectAccessRequest(requestDto);
                var response = controllerMapper.toSarResponse(responseDto);
                
                log.atInfo().log("Subject Access Request processed successfully for requestId: %s", 
                    sarRequest.getRequestId());
                
                return ResponseEntity.ok(response);
                
            } catch (Exception e) {
                log.atSevere().withCause(e).log("Failed to process Subject Access Request for requestId: %s", 
                    sarRequest.getRequestId());
                
                // Return error response
                var errorResponse = new SarResponse()
                    .requestId(sarRequest.getRequestId())
                    .subtaskId(sarRequest.getSubtaskId())
                    .error(true)
                    .reason("Failed to process request: " + e.getMessage());
                
                return ResponseEntity.ok(errorResponse);
            }
        });
        } catch (Exception e) {
            log.atSevere().withCause(e).log("Failed to record timer for Subject Access Request");
            throw new RuntimeException("Failed to process Subject Access Request", e);
        }
    }

    @Override
    public ResponseEntity<DdrResponse> deleteUserData(@Valid @RequestBody DdrRequest ddrRequest) {
        log.atInfo().log("Processing Data Deletion Request for email: %s, requestId: %s", 
            ddrRequest.getEmail(), ddrRequest.getRequestId());
        
        try {
            return gdprRequestsTimer.recordCallable(() -> {
            try {
                Counter.builder("gdpr.requests.total")
                    .tag("type", "ddr")
                    .register(meterRegistry)
                    .increment();
                
                var requestDto = controllerMapper.toDdrRequestDto(ddrRequest);
                var responseDto = gdprService.processDataDeletionRequest(requestDto);
                var response = controllerMapper.toDdrResponse(responseDto);
                
                log.atInfo().log("Data Deletion Request processed successfully for requestId: %s", 
                    ddrRequest.getRequestId());
                
                return ResponseEntity.ok(response);
                
            } catch (Exception e) {
                log.atSevere().withCause(e).log("Failed to process Data Deletion Request for requestId: %s", 
                    ddrRequest.getRequestId());
                
                // Return error response
                var errorResponse = new DdrResponse()
                    .requestId(ddrRequest.getRequestId())
                    .subtaskId(ddrRequest.getSubtaskId())
                    .error(true)
                    .reason("Failed to process request: " + e.getMessage());
                
                return ResponseEntity.ok(errorResponse);
            }
        });
        } catch (Exception e) {
            log.atSevere().withCause(e).log("Failed to record timer for Data Deletion Request");
            throw new RuntimeException("Failed to process Data Deletion Request", e);
        }
    }
}
