package com.gumtree.tns.identityverification.service.impl;

import com.gumtree.tns.identityverification.dao.model.IdentityVerificationRequest;
import com.gumtree.tns.identityverification.dao.model.VerificationStatus;
import com.gumtree.tns.identityverification.model.VerificationType;
import com.gumtree.tns.identityverification.dao.repository.IdentityVerificationRequestRepository;
import com.gumtree.tns.identityverification.dto.InitiateVerificationRequestDto;
import com.gumtree.tns.identityverification.dto.VerificationResponseDto;
import com.gumtree.tns.identityverification.dto.VerificationStatusResponseDto;
import com.gumtree.tns.identityverification.gateway.GbgGateway;
import com.gumtree.tns.identityverification.service.AuditLogService;
import com.gumtree.tns.identityverification.service.IdentityVerificationService;
import com.gumtree.tns.identityverification.service.RiskAssessmentService;
import com.gumtree.tns.identityverification.service.mapper.VerificationMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.flogger.Flogger;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;

/**
 * Implementation of IdentityVerificationService
 */
@Flogger
@Service
@RequiredArgsConstructor
public class IdentityVerificationServiceImpl implements IdentityVerificationService {

    private final IdentityVerificationRequestRepository verificationRequestRepository;
    private final GbgGateway gbgGateway;
    private final RiskAssessmentService riskAssessmentService;
    private final AuditLogService auditLogService;
    private final VerificationMapper verificationMapper;

    @Override
    @Transactional
    public VerificationResponseDto initiateUserVerification(Long userId, InitiateVerificationRequestDto request) {
        log.atInfo().log("Initiating user verification for userId: %d", userId);
        
        // Validate request
        validateVerificationRequest(request, VerificationType.KYC);
        
        // Check for existing verification
        Optional<IdentityVerificationRequest> existingVerification = 
            verificationRequestRepository.findByUserIdAndStatusIn(userId, getActiveStatuses());
        
        if (existingVerification.isPresent()) {
            log.atWarning().log("Active verification already exists for userId: %d", userId);
            return verificationMapper.toVerificationResponseDto(existingVerification.get());
        }
        
        // Create new verification request
        IdentityVerificationRequest verificationRequest = createVerificationRequest(
            userId, null, request);
        
        // Save to database
        verificationRequest = verificationRequestRepository.save(verificationRequest);
        
        // Create GBG profile
        String gbgProfileId = gbgGateway.createProfile(verificationRequest);
        verificationRequest.setGbgProfileId(gbgProfileId);
        verificationRequest = verificationRequestRepository.save(verificationRequest);
        
        // Log audit event
        auditLogService.logVerificationInitiated(verificationRequest, userId, null);
        
        log.atInfo().log("User verification initiated successfully. VerificationId: %s", 
            verificationRequest.getId());
        
        return verificationMapper.toVerificationResponseDto(verificationRequest);
    }

    @Override
    @Transactional
    public VerificationResponseDto initiateAccountVerification(Long accountId, InitiateVerificationRequestDto request) {
        log.atInfo().log("Initiating account verification for accountId: %d", accountId);
        
        // Validate request
        validateVerificationRequest(request, VerificationType.KYB);
        
        // Check for existing verification
        Optional<IdentityVerificationRequest> existingVerification = 
            verificationRequestRepository.findByAccountIdAndStatusIn(accountId, getActiveStatuses());
        
        if (existingVerification.isPresent()) {
            log.atWarning().log("Active verification already exists for accountId: %d", accountId);
            return verificationMapper.toVerificationResponseDto(existingVerification.get());
        }
        
        // Create new verification request
        IdentityVerificationRequest verificationRequest = createVerificationRequest(
            null, accountId, request);
        
        // Save to database
        verificationRequest = verificationRequestRepository.save(verificationRequest);
        
        // Create GBG profile
        String gbgProfileId = gbgGateway.createProfile(verificationRequest);
        verificationRequest.setGbgProfileId(gbgProfileId);
        verificationRequest = verificationRequestRepository.save(verificationRequest);
        
        // Log audit event
        auditLogService.logVerificationInitiated(verificationRequest, null, accountId);
        
        log.atInfo().log("Account verification initiated successfully. VerificationId: %s", 
            verificationRequest.getId());
        
        return verificationMapper.toVerificationResponseDto(verificationRequest);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<VerificationStatusResponseDto> getUserVerificationStatus(Long userId) {
        log.atInfo().log("Getting verification status for userId: %d", userId);
        
        Optional<IdentityVerificationRequest> verificationRequest = 
            verificationRequestRepository.findTopByUserIdOrderByCreatedAtDesc(userId);
        
        return verificationRequest.map(verificationMapper::toVerificationStatusResponseDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<VerificationStatusResponseDto> getAccountVerificationStatus(Long accountId) {
        log.atInfo().log("Getting verification status for accountId: %d", accountId);
        
        Optional<IdentityVerificationRequest> verificationRequest = 
            verificationRequestRepository.findTopByAccountIdOrderByCreatedAtDesc(accountId);
        
        return verificationRequest.map(verificationMapper::toVerificationStatusResponseDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<VerificationStatusResponseDto> getVerificationStatus(UUID verificationId) {
        log.atInfo().log("Getting verification status for verificationId: %s", verificationId);
        
        Optional<IdentityVerificationRequest> verificationRequest = 
            verificationRequestRepository.findById(verificationId);
        
        return verificationRequest.map(verificationMapper::toVerificationStatusResponseDto);
    }

    @Override
    @Transactional
    public VerificationResponseDto submitVerificationRequest(UUID verificationId) {
        log.atInfo().log("Submitting verification request: %s", verificationId);
        
        IdentityVerificationRequest verificationRequest = verificationRequestRepository
            .findById(verificationId)
            .orElseThrow(() -> new IllegalArgumentException("Verification request not found: " + verificationId));
        
        // Validate that request can be submitted
        if (!canSubmitVerification(verificationRequest)) {
            throw new IllegalStateException("Verification request cannot be submitted in current state: " + 
                verificationRequest.getStatus());
        }
        
        // Submit to GBG
        gbgGateway.submitVerification(verificationRequest);
        
        // Update status
        verificationRequest.setStatus(VerificationStatus.IN_PROGRESS);
        verificationRequest.setUpdatedAt(LocalDateTime.now());
        verificationRequest = verificationRequestRepository.save(verificationRequest);
        
        // Log audit event
        auditLogService.logVerificationSubmitted(verificationRequest);
        
        log.atInfo().log("Verification request submitted successfully: %s", verificationId);
        
        return verificationMapper.toVerificationResponseDto(verificationRequest);
    }

    @Override
    @Transactional
    public IdentityVerificationRequest updateVerificationStatus(UUID verificationId, VerificationStatus status) {
        log.atInfo().log("Updating verification status for verificationId: %s to status: %s",
            verificationId, status);

        IdentityVerificationRequest verificationRequest = verificationRequestRepository
            .findById(verificationId)
            .orElseThrow(() -> new IllegalArgumentException("Verification request not found: " + verificationId));

        VerificationStatus oldStatus = verificationRequest.getStatus();
        verificationRequest.setStatus(status);
        verificationRequest.setUpdatedAt(LocalDateTime.now());

        if (isCompletedStatus(status)) {
            verificationRequest.setCompletedAt(LocalDateTime.now());
        }

        verificationRequest = verificationRequestRepository.save(verificationRequest);

        // Log audit event
        auditLogService.logStatusChange(verificationRequest, oldStatus, status);

        // Calculate risk score if completed
        if (VerificationStatus.COMPLETED.equals(status)) {
            riskAssessmentService.calculateAndSaveRiskAssessment(verificationRequest);
        }

        return verificationRequest;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<VerificationStatusResponseDto> getVerificationRequestsForReview(
            VerificationStatus status, Long userId, Long accountId, Pageable pageable) {
        log.atInfo().log("Getting verification requests for review with filters - status: %s, userId: %s, accountId: %s",
            status, userId, accountId);

        Page<IdentityVerificationRequest> requests = verificationRequestRepository
            .findByFilters(status, userId, accountId, pageable);

        return requests.map(verificationMapper::toVerificationStatusResponseDto);
    }

    @Override
    @Transactional
    public void processGbgWebhookEvent(String eventType, String eventData) {
        log.atInfo().log("Processing GBG webhook event: %s", eventType);

        try {
            // Parse event data and update verification status accordingly
            // This would be implemented based on GBG webhook specification

            // Log audit event
            auditLogService.logWebhookEvent(eventType, eventData);

        } catch (Exception e) {
            log.atSevere().withCause(e).log("Failed to process GBG webhook event: %s", eventType);
            throw e;
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Integer calculateRiskScore(UUID verificationId) {
        log.atInfo().log("Calculating risk score for verificationId: %s", verificationId);

        IdentityVerificationRequest verificationRequest = verificationRequestRepository
            .findById(verificationId)
            .orElseThrow(() -> new IllegalArgumentException("Verification request not found: " + verificationId));

        return riskAssessmentService.calculateRiskScore(verificationRequest);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean isVerificationExpired(UUID verificationId) {
        IdentityVerificationRequest verificationRequest = verificationRequestRepository
            .findById(verificationId)
            .orElseThrow(() -> new IllegalArgumentException("Verification request not found: " + verificationId));

        // Check if verification is older than 30 days and not completed
        LocalDateTime expiryDate = verificationRequest.getCreatedAt().plusDays(30);
        return LocalDateTime.now().isAfter(expiryDate) && !verificationRequest.isCompleted();
    }

    @Override
    @Transactional(readOnly = true)
    public Page<VerificationStatusResponseDto> getVerificationRequestsByType(
            VerificationType verificationType, Pageable pageable) {
        log.atInfo().log("Getting verification requests by type: %s", verificationType);

        Page<IdentityVerificationRequest> requests = verificationRequestRepository
            .findByVerificationType(verificationType, pageable);

        return requests.map(verificationMapper::toVerificationStatusResponseDto);
    }

    // Helper methods
    private void validateVerificationRequest(InitiateVerificationRequestDto request, VerificationType expectedType) {
        if (!expectedType.equals(request.getVerificationType())) {
            throw new IllegalArgumentException("Invalid verification type: " + request.getVerificationType());
        }

        if (VerificationType.KYC.equals(expectedType) && request.getPersonalInfo() == null) {
            throw new IllegalArgumentException("Personal info is required for KYC verification");
        }

        if (VerificationType.KYB.equals(expectedType) && request.getBusinessInfo() == null) {
            throw new IllegalArgumentException("Business info is required for KYB verification");
        }
    }

    private IdentityVerificationRequest createVerificationRequest(Long userId, Long accountId,
            InitiateVerificationRequestDto request) {
        return IdentityVerificationRequest.builder()
            .userId(userId)
            .accountId(accountId)
            .verificationType(request.getVerificationType())
            .status(VerificationStatus.INITIATED)
            .customerReference(request.getCustomerReference())
            .personalInfo(request.getPersonalInfo() != null ?
                verificationMapper.toJsonString(request.getPersonalInfo()) : null)
            .businessInfo(request.getBusinessInfo() != null ?
                verificationMapper.toJsonString(request.getBusinessInfo()) : null)
            .createdAt(LocalDateTime.now())
            .updatedAt(LocalDateTime.now())
            .build();
    }

    private java.util.List<VerificationStatus> getActiveStatuses() {
        return java.util.Arrays.asList(
            VerificationStatus.INITIATED,
            VerificationStatus.DOCUMENTS_REQUIRED,
            VerificationStatus.DOCUMENTS_UPLOADED,
            VerificationStatus.IN_PROGRESS,
            VerificationStatus.UNDER_REVIEW
        );
    }

    private boolean canSubmitVerification(IdentityVerificationRequest verificationRequest) {
        return VerificationStatus.DOCUMENTS_UPLOADED.equals(verificationRequest.getStatus()) ||
               VerificationStatus.INITIATED.equals(verificationRequest.getStatus());
    }

    private boolean isCompletedStatus(VerificationStatus status) {
        return VerificationStatus.COMPLETED.equals(status) ||
               VerificationStatus.APPROVED.equals(status) ||
               VerificationStatus.REJECTED.equals(status) ||
               VerificationStatus.EXPIRED.equals(status);
    }
}
