package com.gumtree.tns.identityverification.util;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;

/**
 * Utility class for handling metrics operations
 */
public class MetricsUtil {
    
    /**
     * Increment a counter with tags
     */
    public static void incrementCounter(MeterRegistry meterRegistry, String counterName, String... tags) {
        Counter.Builder builder = Counter.builder(counterName);
        
        // Add tags in pairs
        for (int i = 0; i < tags.length; i += 2) {
            if (i + 1 < tags.length) {
                builder.tag(tags[i], tags[i + 1]);
            }
        }
        
        builder.register(meterRegistry).increment();
    }
}
