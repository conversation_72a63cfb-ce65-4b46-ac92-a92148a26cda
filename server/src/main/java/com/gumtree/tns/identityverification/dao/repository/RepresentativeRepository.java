package com.gumtree.tns.identityverification.dao.repository;

import com.gumtree.tns.identityverification.dao.model.Representative;
import com.gumtree.tns.identityverification.dao.model.RepresentativeType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository interface for Representative entity
 */
@Repository
public interface RepresentativeRepository extends JpaRepository<Representative, UUID> {

    /**
     * Find representatives by verification request ID
     */
    List<Representative> findByVerificationRequestId(UUID verificationRequestId);

    /**
     * Find representatives by verification request ID and type
     */
    List<Representative> findByVerificationRequestIdAndRepresentativeType(
        UUID verificationRequestId, RepresentativeType representativeType);

    /**
     * Find representative by GBG representative ID
     */
    Optional<Representative> findByGbgRepresentativeId(String gbgRepresentativeId);

    /**
     * Find representatives by verification status
     */
    List<Representative> findByVerificationStatus(Representative.VerificationStatus verificationStatus);

    /**
     * Delete representatives by verification request ID
     */
    void deleteByVerificationRequestId(UUID verificationRequestId);

    /**
     * Count representatives by verification request ID
     */
    long countByVerificationRequestId(UUID verificationRequestId);

    /**
     * Count representatives by verification request ID and status
     */
    long countByVerificationRequestIdAndVerificationStatus(
        UUID verificationRequestId, Representative.VerificationStatus verificationStatus);
}
