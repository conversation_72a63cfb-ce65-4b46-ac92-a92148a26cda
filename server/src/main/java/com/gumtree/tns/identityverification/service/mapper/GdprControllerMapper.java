package com.gumtree.tns.identityverification.service.mapper;

import com.gumtree.tns.identityverification.dto.*;
import com.gumtree.tns.identityverification.model.*;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Mapper for GDPR-related API models and DTOs
 */
@Component
public class GdprControllerMapper {

    public SarRequestDto toSarRequestDto(SarRequest request) {
        return SarRequestDto.builder()
            .requestId(request.getRequestId())
            .subtaskId(request.getSubtaskId())
            .email(request.getEmail())
            .build();
    }

    public SarResponse toSarResponse(SarResponseDto dto) {
        return new SarResponse()
            .requestId(dto.getRequestId())
            .subtaskId(dto.getSubtaskId())
            .error(dto.isError())
            .reason(dto.getReason())
            .salutation(dto.getSalutation())
            .personalDataSegments(mapPersonalDataSegments(dto.getPersonalDataSegments()));
    }

    public DdrRequestDto toDdrRequestDto(DdrRequest request) {
        return DdrRequestDto.builder()
            .requestId(request.getRequestId())
            .subtaskId(request.getSubtaskId())
            .email(request.getEmail())
            .build();
    }

    public DdrResponse toDdrResponse(DdrResponseDto dto) {
        return new DdrResponse()
            .requestId(dto.getRequestId())
            .subtaskId(dto.getSubtaskId())
            .error(dto.isError())
            .reason(dto.getReason());
    }

    private List<PersonalDataSegment> mapPersonalDataSegments(List<PersonalDataSegmentDto> segments) {
        if (segments == null) return null;
        
        return segments.stream()
            .map(this::mapPersonalDataSegment)
            .collect(Collectors.toList());
    }

    private PersonalDataSegment mapPersonalDataSegment(PersonalDataSegmentDto segment) {
        return new PersonalDataSegment()
            .title(segment.getTitle())
            .description(segment.getDescription())
            .values(segment.getValues());
    }


}
