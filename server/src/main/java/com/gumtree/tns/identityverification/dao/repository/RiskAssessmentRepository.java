package com.gumtree.tns.identityverification.dao.repository;

import com.gumtree.tns.identityverification.dao.model.RiskAssessment;
import com.gumtree.tns.identityverification.dao.model.RiskLevel;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository interface for RiskAssessment entity
 */
@Repository
public interface RiskAssessmentRepository extends JpaRepository<RiskAssessment, UUID> {

    /**
     * Find risk assessments by verification request ID
     */
    List<RiskAssessment> findByVerificationRequestId(UUID verificationRequestId);

    /**
     * Find latest risk assessment by verification request ID
     */
    Optional<RiskAssessment> findTopByVerificationRequestIdOrderByAssessmentDateDesc(UUID verificationRequestId);

    /**
     * Find risk assessments by risk level
     */
    List<RiskAssessment> findByRiskLevel(RiskLevel riskLevel);

    /**
     * Find risk assessments by risk score range
     */
    @Query("SELECT r FROM RiskAssessment r WHERE r.riskScore BETWEEN :minScore AND :maxScore")
    List<RiskAssessment> findByRiskScoreBetween(@Param("minScore") Integer minScore, @Param("maxScore") Integer maxScore);

    /**
     * Find risk assessments created after a specific date
     */
    List<RiskAssessment> findByAssessmentDateAfter(LocalDateTime assessmentDate);

    /**
     * Delete risk assessments by verification request ID
     */
    void deleteByVerificationRequestId(UUID verificationRequestId);

    /**
     * Count risk assessments by verification request ID
     */
    long countByVerificationRequestId(UUID verificationRequestId);

    /**
     * Get average risk score for a time period
     */
    @Query("SELECT AVG(r.riskScore) FROM RiskAssessment r WHERE r.assessmentDate BETWEEN :startDate AND :endDate")
    Double getAverageRiskScore(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);
}
