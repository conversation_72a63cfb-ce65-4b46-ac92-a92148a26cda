package com.gumtree.tns.identityverification.gateway.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * DTO for GBG Document data
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GbgDocumentDto {

    private String id;

    private String profileId;

    private String documentType;

    private String status;

    private String fileName;

    private String mimeType;

    private Long fileSize;

    private byte[] fileContent;

    private String downloadUrl;

    private LocalDateTime uploadedAt;

    private LocalDateTime expiresAt;

    private String checksum;
}
