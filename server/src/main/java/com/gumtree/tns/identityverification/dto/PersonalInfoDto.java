package com.gumtree.tns.identityverification.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * DTO for personal information in KYC verification
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PersonalInfoDto {

    @NotBlank
    private String firstName;

    @NotBlank
    private String lastName;

    private String middleName;

    @NotNull
    private LocalDate dateOfBirth;

    private String nationality;

    private AddressDto address;

    private String phoneNumber;

    private String email;

    private String placeOfBirth;

    private String gender;

    private String maritalStatus;

    private String occupation;
}
