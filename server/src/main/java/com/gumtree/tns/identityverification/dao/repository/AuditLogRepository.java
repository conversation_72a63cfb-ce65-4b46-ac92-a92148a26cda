package com.gumtree.tns.identityverification.dao.repository;

import com.gumtree.tns.identityverification.dao.model.AuditLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * Repository interface for AuditLog entity
 */
@Repository
public interface AuditLogRepository extends JpaRepository<AuditLog, UUID> {

    /**
     * Find audit logs by verification request ID
     */
    List<AuditLog> findByVerificationRequestId(UUID verificationRequestId);

    /**
     * Find audit logs by user ID
     */
    List<AuditLog> findByUserId(Long userId);

    /**
     * Find audit logs by account ID
     */
    List<AuditLog> findByAccountId(Long accountId);

    /**
     * Find audit logs by action type
     */
    List<AuditLog> findByActionType(String actionType);

    /**
     * Find audit logs by performed by
     */
    List<AuditLog> findByPerformedBy(String performedBy);

    /**
     * Find audit logs created after a specific date
     */
    List<AuditLog> findByCreatedAtAfter(LocalDateTime createdAt);

    /**
     * Find audit logs for a date range
     */
    @Query("SELECT a FROM AuditLog a WHERE a.createdAt BETWEEN :startDate AND :endDate")
    List<AuditLog> findByDateRange(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    /**
     * Find audit logs with pagination
     */
    Page<AuditLog> findByVerificationRequestId(UUID verificationRequestId, Pageable pageable);

    /**
     * Find audit logs by user ID with pagination
     */
    Page<AuditLog> findByUserId(Long userId, Pageable pageable);

    /**
     * Find audit logs by account ID with pagination
     */
    Page<AuditLog> findByAccountId(Long accountId, Pageable pageable);

    /**
     * Count audit logs by action type
     */
    long countByActionType(String actionType);

    /**
     * Count audit logs by user ID
     */
    long countByUserId(Long userId);

    /**
     * Count audit logs by account ID
     */
    long countByAccountId(Long accountId);

    /**
     * Delete old audit logs
     */
    @Query("DELETE FROM AuditLog a WHERE a.createdAt < :cutoffDate")
    void deleteOldAuditLogs(@Param("cutoffDate") LocalDateTime cutoffDate);

    /**
     * Find audit logs created before a specific date
     */
    List<AuditLog> findByCreatedAtBefore(LocalDateTime createdAt);
}
