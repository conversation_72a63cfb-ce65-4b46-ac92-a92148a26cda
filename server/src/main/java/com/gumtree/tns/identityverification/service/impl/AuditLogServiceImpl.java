package com.gumtree.tns.identityverification.service.impl;

import com.gumtree.tns.identityverification.dao.model.AuditLog;
import com.gumtree.tns.identityverification.dao.model.IdentityVerificationRequest;
import com.gumtree.tns.identityverification.dao.model.VerificationDocument;
import com.gumtree.tns.identityverification.dao.model.VerificationStatus;
import com.gumtree.tns.identityverification.dao.repository.AuditLogRepository;
import com.gumtree.tns.identityverification.service.AuditLogService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.flogger.Flogger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * Implementation of AuditLogService
 */
@Flogger
@Service
@RequiredArgsConstructor
public class AuditLogServiceImpl implements AuditLogService {

    private final AuditLogRepository auditLogRepository;
    private final ObjectMapper objectMapper;

    @Override
    @Transactional
    public void logVerificationInitiated(IdentityVerificationRequest verificationRequest, Long userId, Long accountId) {
        Map<String, Object> details = new HashMap<>();
        details.put("verificationType", verificationRequest.getVerificationType());
        details.put("customerReference", verificationRequest.getCustomerReference());
        details.put("gbgProfileId", verificationRequest.getGbgProfileId());
        
        createAuditLog(
            verificationRequest,
            "VERIFICATION_INITIATED",
            details,
            "SYSTEM",
            userId,
            accountId
        );
    }

    @Override
    @Transactional
    public void logVerificationSubmitted(IdentityVerificationRequest verificationRequest) {
        Map<String, Object> details = new HashMap<>();
        details.put("verificationId", verificationRequest.getId());
        details.put("gbgProfileId", verificationRequest.getGbgProfileId());
        
        createAuditLog(
            verificationRequest,
            "VERIFICATION_SUBMITTED",
            details,
            "SYSTEM",
            verificationRequest.getUserId(),
            verificationRequest.getAccountId()
        );
    }

    @Override
    @Transactional
    public void logStatusChange(IdentityVerificationRequest verificationRequest, 
                               VerificationStatus oldStatus, VerificationStatus newStatus) {
        Map<String, Object> details = new HashMap<>();
        details.put("verificationId", verificationRequest.getId());
        details.put("oldStatus", oldStatus);
        details.put("newStatus", newStatus);
        details.put("timestamp", LocalDateTime.now());
        
        createAuditLog(
            verificationRequest,
            "STATUS_CHANGED",
            details,
            "SYSTEM",
            verificationRequest.getUserId(),
            verificationRequest.getAccountId()
        );
    }

    @Override
    @Transactional
    public void logDocumentUploaded(VerificationDocument document) {
        Map<String, Object> details = new HashMap<>();
        details.put("documentId", document.getId());
        details.put("documentType", document.getDocumentType());
        details.put("fileName", document.getFileName());
        details.put("fileSize", document.getFileSize());
        details.put("mimeType", document.getMimeType());
        
        createAuditLog(
            document.getVerificationRequest(),
            "DOCUMENT_UPLOADED",
            details,
            "USER",
            document.getVerificationRequest().getUserId(),
            document.getVerificationRequest().getAccountId()
        );
    }

    @Override
    @Transactional
    public void logDocumentDeleted(VerificationDocument document) {
        Map<String, Object> details = new HashMap<>();
        details.put("documentId", document.getId());
        details.put("documentType", document.getDocumentType());
        details.put("fileName", document.getFileName());
        
        createAuditLog(
            document.getVerificationRequest(),
            "DOCUMENT_DELETED",
            details,
            "ADMIN",
            document.getVerificationRequest().getUserId(),
            document.getVerificationRequest().getAccountId()
        );
    }

    @Override
    @Transactional
    public void logDocumentStatusChanged(VerificationDocument document, 
                                        VerificationDocument.DocumentStatus oldStatus, 
                                        VerificationDocument.DocumentStatus newStatus) {
        Map<String, Object> details = new HashMap<>();
        details.put("documentId", document.getId());
        details.put("oldStatus", oldStatus);
        details.put("newStatus", newStatus);
        details.put("timestamp", LocalDateTime.now());
        
        createAuditLog(
            document.getVerificationRequest(),
            "DOCUMENT_STATUS_CHANGED",
            details,
            "SYSTEM",
            document.getVerificationRequest().getUserId(),
            document.getVerificationRequest().getAccountId()
        );
    }

    @Override
    @Transactional
    public void logSarProcessed(String requestId, String email, int segmentCount) {
        Map<String, Object> details = new HashMap<>();
        details.put("requestId", requestId);
        details.put("email", email);
        details.put("segmentCount", segmentCount);
        details.put("timestamp", LocalDateTime.now());
        
        createAuditLog(
            null,
            "SAR_PROCESSED",
            details,
            "GDPR_SYSTEM",
            null,
            null
        );
    }

    @Override
    @Transactional
    public void logDdrProcessed(String requestId, String email, int deletedRecords) {
        Map<String, Object> details = new HashMap<>();
        details.put("requestId", requestId);
        details.put("email", email);
        details.put("deletedRecords", deletedRecords);
        details.put("timestamp", LocalDateTime.now());
        
        createAuditLog(
            null,
            "DDR_PROCESSED",
            details,
            "GDPR_SYSTEM",
            null,
            null
        );
    }

    @Override
    @Transactional
    public void logWebhookEvent(String eventType, String eventData) {
        Map<String, Object> details = new HashMap<>();
        details.put("eventType", eventType);
        details.put("eventData", eventData);
        details.put("timestamp", LocalDateTime.now());
        
        createAuditLog(
            null,
            "WEBHOOK_RECEIVED",
            details,
            "GBG_SYSTEM",
            null,
            null
        );
    }

    @Override
    @Transactional(readOnly = true)
    public List<AuditLog> getAuditLogsForVerification(UUID verificationId) {
        return auditLogRepository.findByVerificationRequestId(verificationId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<AuditLog> getAuditLogsForUser(Long userId) {
        return auditLogRepository.findByUserId(userId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<AuditLog> getAuditLogsForAccount(Long accountId) {
        return auditLogRepository.findByAccountId(accountId);
    }

    @Override
    @Transactional
    public long cleanupOldAuditLogs(int retentionDays) {
        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(retentionDays);
        
        List<AuditLog> oldLogs = auditLogRepository.findByCreatedAtBefore(cutoffDate);
        long count = oldLogs.size();
        
        auditLogRepository.deleteOldAuditLogs(cutoffDate);
        
        log.atInfo().log("Cleaned up %d old audit logs older than %d days", count, retentionDays);
        
        return count;
    }

    private void createAuditLog(IdentityVerificationRequest verificationRequest,
                               String actionType,
                               Map<String, Object> details,
                               String performedBy,
                               Long userId,
                               Long accountId) {
        try {
            String actionDetailsJson = objectMapper.writeValueAsString(details);
            
            AuditLog auditLog = AuditLog.builder()
                .verificationRequest(verificationRequest)
                .actionType(actionType)
                .actionDetails(actionDetailsJson)
                .performedBy(performedBy)
                .userId(userId)
                .accountId(accountId)
                .createdAt(LocalDateTime.now())
                .build();
            
            auditLogRepository.save(auditLog);
            
        } catch (Exception e) {
            log.atSevere().withCause(e).log("Failed to create audit log for action: %s", actionType);
        }
    }
}
