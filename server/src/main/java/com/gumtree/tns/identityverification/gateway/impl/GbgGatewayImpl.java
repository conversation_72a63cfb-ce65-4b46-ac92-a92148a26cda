package com.gumtree.tns.identityverification.gateway.impl;

import com.gumtree.tns.identityverification.dao.model.IdentityVerificationRequest;
import com.gumtree.tns.identityverification.gateway.GbgGateway;
import com.gumtree.tns.identityverification.gateway.client.GbgApiClient;
import com.gumtree.tns.identityverification.gateway.dto.GbgProfileDto;
import com.gumtree.tns.identityverification.gateway.dto.GbgRepresentativeDto;
import com.gumtree.tns.identityverification.gateway.dto.GbgVerificationDto;
import com.gumtree.tns.identityverification.gateway.dto.GbgDocumentDto;
import com.gumtree.tns.identityverification.gateway.mapper.GbgMapper;
import com.gumtree.tns.identityverification.util.MetricsUtil;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.RequiredArgsConstructor;
import lombok.extern.flogger.Flogger;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * Implementation of GbgGateway
 */
@Flogger
@Component
public class GbgGatewayImpl implements GbgGateway {

    private final GbgApiClient gbgApiClient;
    private final GbgMapper gbgMapper;
    private final MeterRegistry meterRegistry;

    // Metrics
    private final Counter gbgApiCallsCounter;
    private final Counter gbgApiErrorsCounter;
    private final Timer gbgApiCallsTimer;

    public GbgGatewayImpl(GbgApiClient gbgApiClient, GbgMapper gbgMapper, MeterRegistry meterRegistry) {
        this.gbgApiClient = gbgApiClient;
        this.gbgMapper = gbgMapper;
        this.meterRegistry = meterRegistry;
        
        // Initialize metrics
        this.gbgApiCallsCounter = Counter.builder("gbg.api.calls.total")
            .description("Total number of GBG API calls")
            .register(meterRegistry);
        
        this.gbgApiErrorsCounter = Counter.builder("gbg.api.errors.total")
            .description("Total number of GBG API errors")
            .register(meterRegistry);
        
        this.gbgApiCallsTimer = Timer.builder("gbg.api.calls.duration")
            .description("Duration of GBG API calls")
            .register(meterRegistry);
    }

    @Override
    public String createProfile(IdentityVerificationRequest verificationRequest) {
        log.atInfo().log("Creating GBG profile for verification request: %s", verificationRequest.getId());
        
        try {
            return gbgApiCallsTimer.recordCallable(() -> {
            try {
                MetricsUtil.incrementCounter(meterRegistry, "gbg.api.calls.total", "operation", "create_profile");
                
                // Map verification request to GBG profile creation request
                var profileRequest = gbgMapper.toGbgProfileCreationRequest(verificationRequest);
                
                // Call GBG API to create profile
                var profileResponse = gbgApiClient.createProfile(profileRequest);
                
                log.atInfo().log("GBG profile created successfully. ProfileId: %s", profileResponse.getId());
                return profileResponse.getId();
                
            } catch (Exception e) {
                MetricsUtil.incrementCounter(meterRegistry, "gbg.api.errors.total", "operation", "create_profile");
                log.atSevere().withCause(e).log("Failed to create GBG profile for verification request: %s", 
                    verificationRequest.getId());
                throw new RuntimeException("Failed to create GBG profile", e);
            }
        });
        } catch (Exception e) {
            log.atSevere().withCause(e).log("Failed to record timer");
            throw new RuntimeException("Failed to process request", e);
        }
    }

    @Override
    public Optional<GbgProfileDto> getProfile(String gbgProfileId) {
        log.atInfo().log("Getting GBG profile: %s", gbgProfileId);
        
        try {
            return gbgApiCallsTimer.recordCallable(() -> {
            try {
                MetricsUtil.incrementCounter(meterRegistry, "gbg.api.calls.total", "operation", "get_profile");
                
                var profileResponse = gbgApiClient.getProfile(gbgProfileId);
                return Optional.of(gbgMapper.toGbgProfileDto(profileResponse));
                
            } catch (Exception e) {
                MetricsUtil.incrementCounter(meterRegistry, "gbg.api.errors.total", "operation", "get_profile");
                log.atWarning().withCause(e).log("Failed to get GBG profile: %s", gbgProfileId);
                return Optional.empty();
            }
        });
        } catch (Exception e) {
            log.atSevere().withCause(e).log("Failed to record timer");
            throw new RuntimeException("Failed to process request", e);
        }
    }

    @Override
    public GbgProfileDto updateProfile(String gbgProfileId, IdentityVerificationRequest verificationRequest) {
        log.atInfo().log("Updating GBG profile: %s", gbgProfileId);
        
        try {
            return gbgApiCallsTimer.recordCallable(() -> {
            try {
                // TODO: Fix counter increment call
                // gbgApiCallsCounter.increment("operation", "update_profile");
                
                var updateRequest = gbgMapper.toGbgProfileUpdateRequest(verificationRequest);
                var profileResponse = gbgApiClient.updateProfile(gbgProfileId, updateRequest);
                
                log.atInfo().log("GBG profile updated successfully: %s", gbgProfileId);
                return gbgMapper.toGbgProfileDto(profileResponse);
                
            } catch (Exception e) {
                // TODO: Fix counter increment call
                // gbgApiErrorsCounter.increment("operation", "update_profile");
                log.atSevere().withCause(e).log("Failed to update GBG profile: %s", gbgProfileId);
                throw new RuntimeException("Failed to update GBG profile", e);
            }
        });
        } catch (Exception e) {
            log.atSevere().withCause(e).log("Failed to record timer");
            throw new RuntimeException("Failed to process request", e);
        }
    }

    @Override
    public String createRepresentative(String gbgProfileId, GbgRepresentativeDto representativeData) {
        log.atInfo().log("Creating representative for GBG profile: %s", gbgProfileId);
        
        try {
            return gbgApiCallsTimer.recordCallable(() -> {
            try {
                // TODO: Fix counter increment call
                // gbgApiCallsCounter.increment("operation", "create_representative");
                
                var representativeRequest = gbgMapper.toGbgRepresentativeCreationRequest(representativeData);
                var representativeResponse = gbgApiClient.createRepresentative(representativeRequest);
                
                log.atInfo().log("Representative created successfully for profile: %s", gbgProfileId);
                return representativeResponse.getId();
                
            } catch (Exception e) {
                // TODO: Fix counter increment call
                // gbgApiErrorsCounter.increment("operation", "create_representative");
                log.atSevere().withCause(e).log("Failed to create representative for profile: %s", gbgProfileId);
                throw new RuntimeException("Failed to create representative", e);
            }
        });
        } catch (Exception e) {
            log.atSevere().withCause(e).log("Failed to record timer");
            throw new RuntimeException("Failed to process request", e);
        }
    }

    @Override
    public List<GbgRepresentativeDto> getRepresentatives(String gbgProfileId) {
        log.atInfo().log("Getting representatives for GBG profile: %s", gbgProfileId);
        
        try {
            return gbgApiCallsTimer.recordCallable(() -> {
            try {
                // TODO: Fix counter increment call
                // gbgApiCallsCounter.increment("operation", "get_representatives");
                
                var representativesResponse = gbgApiClient.getRepresentatives(gbgProfileId);
                return gbgMapper.toGbgRepresentativeDtoList(representativesResponse);
                
            } catch (Exception e) {
                // TODO: Fix counter increment call
                // gbgApiErrorsCounter.increment("operation", "get_representatives");
                log.atWarning().withCause(e).log("Failed to get representatives for profile: %s", gbgProfileId);
                return List.of();
            }
        });
        } catch (Exception e) {
            log.atSevere().withCause(e).log("Failed to record timer");
            throw new RuntimeException("Failed to process request", e);
        }
    }

    @Override
    public List<GbgVerificationDto> getRepresentativeVerifications(String gbgRepresentativeId) {
        log.atInfo().log("Getting verifications for representative: %s", gbgRepresentativeId);
        
        try {
            return gbgApiCallsTimer.recordCallable(() -> {
            try {
                // TODO: Fix counter increment call
                // gbgApiCallsCounter.increment("operation", "get_representative_verifications");
                
                var verificationsResponse = gbgApiClient.getRepresentativeVerifications(gbgRepresentativeId);
                return gbgMapper.toGbgVerificationDtoList(verificationsResponse);
                
            } catch (Exception e) {
                // TODO: Fix counter increment call
                // gbgApiErrorsCounter.increment("operation", "get_representative_verifications");
                log.atWarning().withCause(e).log("Failed to get verifications for representative: %s", gbgRepresentativeId);
                return List.of();
            }
        });
        } catch (Exception e) {
            log.atSevere().withCause(e).log("Failed to record timer");
            throw new RuntimeException("Failed to process request", e);
        }
    }

    @Override
    public String uploadDocument(String gbgProfileId, GbgDocumentDto documentData) {
        log.atInfo().log("Uploading document for GBG profile: %s", gbgProfileId);
        
        try {
            return gbgApiCallsTimer.recordCallable(() -> {
            try {
                // TODO: Fix counter increment call
                // gbgApiCallsCounter.increment("operation", "upload_document");
                
                var documentRequest = gbgMapper.toGbgDocumentUploadRequest(documentData);
                var documentResponse = gbgApiClient.uploadDocument(documentRequest);
                
                log.atInfo().log("Document uploaded successfully for profile: %s", gbgProfileId);
                return documentResponse.getId();
                
            } catch (Exception e) {
                // TODO: Fix counter increment call
                // gbgApiErrorsCounter.increment("operation", "upload_document");
                log.atSevere().withCause(e).log("Failed to upload document for profile: %s", gbgProfileId);
                throw new RuntimeException("Failed to upload document", e);
            }
        });
        } catch (Exception e) {
            log.atSevere().withCause(e).log("Failed to record timer");
            throw new RuntimeException("Failed to process request", e);
        }
    }

    @Override
    public Optional<GbgDocumentDto> getDocument(String gbgDocumentId) {
        log.atInfo().log("Getting GBG document: %s", gbgDocumentId);
        
        try {
            return gbgApiCallsTimer.recordCallable(() -> {
            try {
                // TODO: Fix counter increment call
                // gbgApiCallsCounter.increment("operation", "get_document");
                
                var documentResponse = gbgApiClient.getDocument(gbgDocumentId);
                return Optional.of(gbgMapper.toGbgDocumentDto(documentResponse));
                
            } catch (Exception e) {
                // TODO: Fix counter increment call
                // gbgApiErrorsCounter.increment("operation", "get_document");
                log.atWarning().withCause(e).log("Failed to get GBG document: %s", gbgDocumentId);
                return Optional.empty();
            }
        });
        } catch (Exception e) {
            log.atSevere().withCause(e).log("Failed to record timer");
            throw new RuntimeException("Failed to process request", e);
        }
    }

    @Override
    public List<GbgDocumentDto> getProfileDocuments(String gbgProfileId) {
        log.atInfo().log("Getting documents for GBG profile: %s", gbgProfileId);
        
        try {
            return gbgApiCallsTimer.recordCallable(() -> {
            try {
                // TODO: Fix counter increment call
                // gbgApiCallsCounter.increment("operation", "get_profile_documents");
                
                var documentsResponse = gbgApiClient.getProfileDocuments(gbgProfileId);
                return gbgMapper.toGbgDocumentDtoList(documentsResponse);
                
            } catch (Exception e) {
                // TODO: Fix counter increment call
                // gbgApiErrorsCounter.increment("operation", "get_profile_documents");
                log.atWarning().withCause(e).log("Failed to get documents for profile: %s", gbgProfileId);
                return List.of();
            }
        });
        } catch (Exception e) {
            log.atSevere().withCause(e).log("Failed to record timer");
            throw new RuntimeException("Failed to process request", e);
        }
    }

    @Override
    public GbgVerificationDto submitVerification(IdentityVerificationRequest verificationRequest) {
        log.atInfo().log("Submitting verification to GBG for request: %s", verificationRequest.getId());

        try {
            return gbgApiCallsTimer.recordCallable(() -> {
            try {
                // TODO: Fix counter increment call
                // gbgApiCallsCounter.increment("operation", "submit_verification");

                var verificationSubmissionRequest = gbgMapper.toGbgVerificationSubmissionRequest(verificationRequest);
                var verificationResponse = gbgApiClient.submitVerification(verificationSubmissionRequest);

                log.atInfo().log("Verification submitted successfully for request: %s", verificationRequest.getId());
                return gbgMapper.toGbgVerificationDto(verificationResponse);

            } catch (Exception e) {
                // TODO: Fix counter increment call
                // gbgApiErrorsCounter.increment("operation", "submit_verification");
                log.atSevere().withCause(e).log("Failed to submit verification for request: %s", verificationRequest.getId());
                throw new RuntimeException("Failed to submit verification", e);
            }
        });
        } catch (Exception e) {
            log.atSevere().withCause(e).log("Failed to record timer");
            throw new RuntimeException("Failed to process request", e);
        }
    }

    @Override
    public Optional<GbgVerificationDto> getVerificationStatus(String gbgVerificationId) {
        log.atInfo().log("Getting verification status from GBG: %s", gbgVerificationId);

        try {
            return gbgApiCallsTimer.recordCallable(() -> {
            try {
                // TODO: Fix counter increment call
                // gbgApiCallsCounter.increment("operation", "get_verification_status");

                var verificationResponse = gbgApiClient.getVerificationStatus(gbgVerificationId);
                return Optional.of(gbgMapper.toGbgVerificationDto(verificationResponse));

            } catch (Exception e) {
                // TODO: Fix counter increment call
                // gbgApiErrorsCounter.increment("operation", "get_verification_status");
                log.atWarning().withCause(e).log("Failed to get verification status: %s", gbgVerificationId);
                return Optional.empty();
            }
        });
        } catch (Exception e) {
            log.atSevere().withCause(e).log("Failed to record timer");
            throw new RuntimeException("Failed to process request", e);
        }
    }

    @Override
    public List<GbgProfileDto> searchProfiles(String searchCriteria) {
        log.atInfo().log("Searching GBG profiles with criteria: %s", searchCriteria);

        try {
            return gbgApiCallsTimer.recordCallable(() -> {
            try {
                // TODO: Fix counter increment call
                // gbgApiCallsCounter.increment("operation", "search_profiles");

                var searchRequest = gbgMapper.toGbgSearchRequest(searchCriteria);
                var searchResponse = gbgApiClient.searchProfiles(searchRequest);

                return gbgMapper.toGbgProfileDtoList(searchResponse);

            } catch (Exception e) {
                // TODO: Fix counter increment call
                // gbgApiErrorsCounter.increment("operation", "search_profiles");
                log.atWarning().withCause(e).log("Failed to search profiles with criteria: %s", searchCriteria);
                return List.of();
            }
        });
        } catch (Exception e) {
            log.atSevere().withCause(e).log("Failed to record timer");
            throw new RuntimeException("Failed to process request", e);
        }
    }

    @Override
    public Optional<String> getProfileDomain(String gbgProfileId) {
        log.atInfo().log("Getting domain for GBG profile: %s", gbgProfileId);

        try {
            return gbgApiCallsTimer.recordCallable(() -> {
            try {
                // TODO: Fix counter increment call
                // gbgApiCallsCounter.increment("operation", "get_profile_domain");

                var domainResponse = gbgApiClient.getProfileDomain(gbgProfileId);
                return Optional.ofNullable(domainResponse.getDomain());

            } catch (Exception e) {
                // TODO: Fix counter increment call
                // gbgApiErrorsCounter.increment("operation", "get_profile_domain");
                log.atWarning().withCause(e).log("Failed to get domain for profile: %s", gbgProfileId);
                return Optional.empty();
            }
        });
        } catch (Exception e) {
            log.atSevere().withCause(e).log("Failed to record timer");
            throw new RuntimeException("Failed to process request", e);
        }
    }

    @Override
    public boolean createProfileDomain(String gbgProfileId, String domain) {
        log.atInfo().log("Creating domain for GBG profile: %s, domain: %s", gbgProfileId, domain);

        try {
            return gbgApiCallsTimer.recordCallable(() -> {
            try {
                // TODO: Fix counter increment call
                // gbgApiCallsCounter.increment("operation", "create_profile_domain");

                var domainRequest = gbgMapper.toGbgDomainCreationRequest(domain);
                gbgApiClient.createProfileDomain(gbgProfileId, domainRequest);

                log.atInfo().log("Domain created successfully for profile: %s", gbgProfileId);
                return true;

            } catch (Exception e) {
                // TODO: Fix counter increment call
                // gbgApiErrorsCounter.increment("operation", "create_profile_domain");
                log.atSevere().withCause(e).log("Failed to create domain for profile: %s", gbgProfileId);
                return false;
            }
        });
        } catch (Exception e) {
            log.atSevere().withCause(e).log("Failed to record timer");
            throw new RuntimeException("Failed to process request", e);
        }
    }

    @Override
    public List<String> getProfileAnalysis(String gbgProfileId) {
        log.atInfo().log("Getting analysis for GBG profile: %s", gbgProfileId);

        try {
            return gbgApiCallsTimer.recordCallable(() -> {
            try {
                // TODO: Fix counter increment call
                // gbgApiCallsCounter.increment("operation", "get_profile_analysis");

                var analysisResponse = gbgApiClient.getProfileAnalysis(gbgProfileId);
                return gbgMapper.toAnalysisList(analysisResponse);

            } catch (Exception e) {
                // TODO: Fix counter increment call
                // gbgApiErrorsCounter.increment("operation", "get_profile_analysis");
                log.atWarning().withCause(e).log("Failed to get analysis for profile: %s", gbgProfileId);
                return List.of();
            }
        });
        } catch (Exception e) {
            log.atSevere().withCause(e).log("Failed to record timer");
            throw new RuntimeException("Failed to process request", e);
        }
    }

    @Override
    public List<String> getProfileChecklists(String gbgProfileId) {
        log.atInfo().log("Getting checklists for GBG profile: %s", gbgProfileId);

        try {
            return gbgApiCallsTimer.recordCallable(() -> {
            try {
                // TODO: Fix counter increment call
                // gbgApiCallsCounter.increment("operation", "get_profile_checklists");

                var checklistsResponse = gbgApiClient.getProfileChecklists(gbgProfileId);
                return gbgMapper.toChecklistsList(checklistsResponse);

            } catch (Exception e) {
                // TODO: Fix counter increment call
                // gbgApiErrorsCounter.increment("operation", "get_profile_checklists");
                log.atWarning().withCause(e).log("Failed to get checklists for profile: %s", gbgProfileId);
                return List.of();
            }
        });
        } catch (Exception e) {
            log.atSevere().withCause(e).log("Failed to record timer");
            throw new RuntimeException("Failed to process request", e);
        }
    }

    @Override
    public List<String> getProfileForms(String gbgProfileId) {
        log.atInfo().log("Getting forms for GBG profile: %s", gbgProfileId);

        try {
            return gbgApiCallsTimer.recordCallable(() -> {
            try {
                // TODO: Fix counter increment call
                // gbgApiCallsCounter.increment("operation", "get_profile_forms");

                var formsResponse = gbgApiClient.getProfileForms(gbgProfileId);
                return gbgMapper.toFormsList(formsResponse);

            } catch (Exception e) {
                // TODO: Fix counter increment call
                // gbgApiErrorsCounter.increment("operation", "get_profile_forms");
                log.atWarning().withCause(e).log("Failed to get forms for profile: %s", gbgProfileId);
                return List.of();
            }
        });
        } catch (Exception e) {
            log.atSevere().withCause(e).log("Failed to record timer");
            throw new RuntimeException("Failed to process request", e);
        }
    }

}