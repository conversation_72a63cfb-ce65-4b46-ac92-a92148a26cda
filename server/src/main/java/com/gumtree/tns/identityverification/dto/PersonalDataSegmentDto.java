package com.gumtree.tns.identityverification.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * DTO for personal data segment in GDPR response
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PersonalDataSegmentDto {

    private String title;

    private String description;

    private List<Map<String, String>> values;

    private String category;

    private String dataSource;

    private String retentionPeriod;

    // Manual builder method for compilation compatibility
    public static PersonalDataSegmentDtoBuilder builder() {
        return new PersonalDataSegmentDtoBuilder();
    }

    public static class PersonalDataSegmentDtoBuilder {
        private String title;
        private String description;
        private List<Map<String, String>> values;
        private String category;
        private String dataSource;
        private String retentionPeriod;

        public PersonalDataSegmentDtoBuilder title(String title) {
            this.title = title;
            return this;
        }

        public PersonalDataSegmentDtoBuilder description(String description) {
            this.description = description;
            return this;
        }

        public PersonalDataSegmentDtoBuilder values(List<Map<String, String>> values) {
            this.values = values;
            return this;
        }

        public PersonalDataSegmentDtoBuilder category(String category) {
            this.category = category;
            return this;
        }

        public PersonalDataSegmentDtoBuilder dataSource(String dataSource) {
            this.dataSource = dataSource;
            return this;
        }

        public PersonalDataSegmentDtoBuilder retentionPeriod(String retentionPeriod) {
            this.retentionPeriod = retentionPeriod;
            return this;
        }

        public PersonalDataSegmentDto build() {
            return new PersonalDataSegmentDto(title, description, values, category, dataSource, retentionPeriod);
        }
    }
}
