package com.gumtree.tns.identityverification.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;

/**
 * DTO for Data Deletion Request
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DdrRequestDto {

    @NotBlank
    private String requestId;

    @NotBlank
    private String subtaskId;

    @NotBlank
    @Email
    private String email;

    private String firstName;

    private String lastName;

    private String phoneNumber;

    private String reason;

    // Manual getters for compilation compatibility
    public String getRequestId() {
        return requestId;
    }

    public String getSubtaskId() {
        return subtaskId;
    }

    public String getEmail() {
        return email;
    }
}
