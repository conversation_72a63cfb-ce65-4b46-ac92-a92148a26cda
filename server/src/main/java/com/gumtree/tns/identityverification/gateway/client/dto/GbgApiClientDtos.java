package com.gumtree.tns.identityverification.gateway.client.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Collection of GBG API Client DTOs
 */
public class GbgApiClientDtos {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgApiProfileUpdateRequest {
        private String personalInfo;
        private String businessInfo;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgApiRepresentativeRequest {
        private String profileId;
        private String type;
        private Map<String, Object> personalInfo;
        private String position;
        private String shareholding;
        private Boolean isSignatory;
        private Boolean isPep;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgApiRepresentativeResponse {
        private String id;
        private String profileId;
        private String type;
        private String status;
        private Map<String, Object> personalInfo;
        private String position;
        private String shareholding;
        private Boolean isSignatory;
        private Boolean isPep;
        private LocalDateTime createdAt;
        private LocalDateTime updatedAt;
        private Map<String, Object> metadata;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgApiDocumentRequest {
        private String profileId;
        private String documentType;
        private String fileName;
        private String mimeType;
        private byte[] fileContent;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgApiDocumentResponse {
        private String id;
        private String profileId;
        private String documentType;
        private String status;
        private String fileName;
        private String mimeType;
        private Long fileSize;
        private String downloadUrl;
        private LocalDateTime uploadedAt;
        private LocalDateTime expiresAt;
        private String checksum;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgApiVerificationRequest {
        private String profileId;
        private String type;
        private String customerReference;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgApiVerificationResponse {
        private String id;
        private String profileId;
        private String representativeId;
        private String type;
        private String status;
        private String result;
        private Integer score;
        private Map<String, Object> details;
        private LocalDateTime createdAt;
        private LocalDateTime updatedAt;
        private LocalDateTime completedAt;
        private Map<String, Object> metadata;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgApiSearchRequest {
        private String query;
        private Integer limit;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgApiDomainRequest {
        private String domain;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgApiDomainResponse {
        private String domain;
        private String status;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgApiAnalysisResponse {
        private String analysisType;
        private String result;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgApiChecklistResponse {
        private String checklistName;
        private String status;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgApiFormResponse {
        private String formName;
        private String status;
    }
}
