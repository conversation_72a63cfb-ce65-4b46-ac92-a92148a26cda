package com.gumtree.tns.identityverification.gateway.client;

import com.gumtree.tns.identityverification.gateway.client.dto.*;
import com.gumtree.tns.identityverification.gateway.client.dto.GbgApiClientDtos.*;

import java.util.List;

/**
 * Client interface for GBG API operations
 */
public interface GbgApiClient {

    /**
     * Create a new profile in GBG
     */
    GbgApiProfileResponse createProfile(GbgApiProfileRequest request);

    /**
     * Get profile details from GBG
     */
    GbgApiProfileResponse getProfile(String profileId);

    /**
     * Update profile in GBG
     */
    GbgApiProfileResponse updateProfile(String profileId, GbgApiProfileUpdateRequest request);

    /**
     * Create a representative for a profile
     */
    GbgApiRepresentativeResponse createRepresentative(GbgApiRepresentativeRequest request);

    /**
     * Get representatives for a profile
     */
    List<GbgApiRepresentativeResponse> getRepresentatives(String profileId);

    /**
     * Get representative verifications
     */
    List<GbgApiVerificationResponse> getRepresentativeVerifications(String representativeId);

    /**
     * Upload document to GBG
     */
    GbgApiDocumentResponse uploadDocument(GbgApiDocumentRequest request);

    /**
     * Get document details from GBG
     */
    GbgApiDocumentResponse getDocument(String documentId);

    /**
     * Get all documents for a profile
     */
    List<GbgApiDocumentResponse> getProfileDocuments(String profileId);

    /**
     * Submit verification request to GBG
     */
    GbgApiVerificationResponse submitVerification(GbgApiVerificationRequest request);

    /**
     * Get verification status from GBG
     */
    GbgApiVerificationResponse getVerificationStatus(String verificationId);

    /**
     * Search for entities in GBG
     */
    List<GbgApiProfileResponse> searchProfiles(GbgApiSearchRequest request);

    /**
     * Get profile domain information
     */
    GbgApiDomainResponse getProfileDomain(String profileId);

    /**
     * Create profile domain
     */
    void createProfileDomain(String profileId, GbgApiDomainRequest request);

    /**
     * Get profile analysis
     */
    List<GbgApiAnalysisResponse> getProfileAnalysis(String profileId);

    /**
     * Get profile checklists
     */
    List<GbgApiChecklistResponse> getProfileChecklists(String profileId);

    /**
     * Get profile forms
     */
    List<GbgApiFormResponse> getProfileForms(String profileId);
}
