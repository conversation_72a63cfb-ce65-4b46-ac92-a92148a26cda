package com.gumtree.tns.identityverification.exception;

/**
 * Exception thrown when there's an error uploading documents
 */
public class DocumentUploadException extends RuntimeException {

    public DocumentUploadException(String message) {
        super(message);
    }

    public DocumentUploadException(String message, Throwable cause) {
        super(message, cause);
    }

    public static DocumentUploadException invalidFileType(String fileName, String actualType, String[] allowedTypes) {
        return new DocumentUploadException(
            String.format("Invalid file type for %s: %s. Allowed types: %s", 
                fileName, actualType, String.join(", ", allowedTypes)));
    }

    public static DocumentUploadException fileTooLarge(String fileName, long actualSize, long maxSize) {
        return new DocumentUploadException(
            String.format("File %s is too large: %d bytes. Maximum allowed: %d bytes", 
                fileName, actualSize, maxSize));
    }

    public static DocumentUploadException uploadFailed(String fileName, Throwable cause) {
        return new DocumentUploadException("Failed to upload document: " + fileName, cause);
    }

    public static DocumentUploadException virusScanFailed(String fileName) {
        return new DocumentUploadException("Document failed virus scan: " + fileName);
    }
}
