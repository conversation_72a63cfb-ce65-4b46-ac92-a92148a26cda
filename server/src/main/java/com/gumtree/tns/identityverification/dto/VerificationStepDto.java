package com.gumtree.tns.identityverification.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * DTO for verification step information
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VerificationStepDto {

    private String stepType;

    private String status;

    private String description;

    private LocalDateTime completedAt;

    private String gbgVerificationId;

    private Object providerData;

    private String errorMessage;

    private Integer retryCount;
}
