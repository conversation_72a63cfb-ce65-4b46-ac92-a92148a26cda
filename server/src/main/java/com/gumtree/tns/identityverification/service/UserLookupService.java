package com.gumtree.tns.identityverification.service;

import java.util.List;
import java.util.Optional;

/**
 * Service interface for user lookup operations
 * This service integrates with external user management systems
 */
public interface UserLookupService {

    /**
     * Find user IDs by email address
     *
     * @param email the email address
     * @return list of user IDs associated with the email
     */
    List<Long> findUserIdsByEmail(String email);

    /**
     * Find account IDs by email address
     *
     * @param email the email address
     * @return list of account IDs associated with the email
     */
    List<Long> findAccountIdsByEmail(String email);

    /**
     * Get user details by user ID
     *
     * @param userId the user ID
     * @return user details if found
     */
    Optional<UserDetails> getUserDetails(Long userId);

    /**
     * Get account details by account ID
     *
     * @param accountId the account ID
     * @return account details if found
     */
    Optional<AccountDetails> getAccountDetails(Long accountId);

    /**
     * Check if user exists
     *
     * @param userId the user ID
     * @return true if user exists
     */
    boolean userExists(Long userId);

    /**
     * Check if account exists
     *
     * @param accountId the account ID
     * @return true if account exists
     */
    boolean accountExists(Long accountId);

    /**
     * Get user email by user ID
     *
     * @param userId the user ID
     * @return user email if found
     */
    Optional<String> getUserEmail(Long userId);

    /**
     * Get account email by account ID
     *
     * @param accountId the account ID
     * @return account email if found
     */
    Optional<String> getAccountEmail(Long accountId);

    /**
     * User details DTO
     */
    class UserDetails {
        private Long userId;
        private String email;
        private String firstName;
        private String lastName;
        private String phoneNumber;
        private boolean active;

        // Constructors, getters, setters
        public UserDetails() {}

        public UserDetails(Long userId, String email, String firstName, String lastName, String phoneNumber, boolean active) {
            this.userId = userId;
            this.email = email;
            this.firstName = firstName;
            this.lastName = lastName;
            this.phoneNumber = phoneNumber;
            this.active = active;
        }

        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        public String getFirstName() { return firstName; }
        public void setFirstName(String firstName) { this.firstName = firstName; }
        public String getLastName() { return lastName; }
        public void setLastName(String lastName) { this.lastName = lastName; }
        public String getPhoneNumber() { return phoneNumber; }
        public void setPhoneNumber(String phoneNumber) { this.phoneNumber = phoneNumber; }
        public boolean isActive() { return active; }
        public void setActive(boolean active) { this.active = active; }
    }

    /**
     * Account details DTO
     */
    class AccountDetails {
        private Long accountId;
        private String email;
        private String companyName;
        private String contactName;
        private String phoneNumber;
        private boolean active;

        // Constructors, getters, setters
        public AccountDetails() {}

        public AccountDetails(Long accountId, String email, String companyName, String contactName, String phoneNumber, boolean active) {
            this.accountId = accountId;
            this.email = email;
            this.companyName = companyName;
            this.contactName = contactName;
            this.phoneNumber = phoneNumber;
            this.active = active;
        }

        public Long getAccountId() { return accountId; }
        public void setAccountId(Long accountId) { this.accountId = accountId; }
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        public String getCompanyName() { return companyName; }
        public void setCompanyName(String companyName) { this.companyName = companyName; }
        public String getContactName() { return contactName; }
        public void setContactName(String contactName) { this.contactName = contactName; }
        public String getPhoneNumber() { return phoneNumber; }
        public void setPhoneNumber(String phoneNumber) { this.phoneNumber = phoneNumber; }
        public boolean isActive() { return active; }
        public void setActive(boolean active) { this.active = active; }
    }
}
