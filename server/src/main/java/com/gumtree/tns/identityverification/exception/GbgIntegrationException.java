package com.gumtree.tns.identityverification.exception;

/**
 * Exception thrown when there's an error integrating with GBG services
 */
public class GbgIntegrationException extends RuntimeException {

    public GbgIntegrationException(String message) {
        super(message);
    }

    public GbgIntegrationException(String message, Throwable cause) {
        super(message, cause);
    }

    public static GbgIntegrationException apiError(String operation, int statusCode, String response) {
        return new GbgIntegrationException(
            String.format("GBG API error during %s: HTTP %d - %s", operation, statusCode, response));
    }

    public static GbgIntegrationException timeout(String operation) {
        return new GbgIntegrationException("GBG API timeout during operation: " + operation);
    }

    public static GbgIntegrationException connectionError(String operation, Throwable cause) {
        return new GbgIntegrationException("GBG API connection error during operation: " + operation, cause);
    }

    public static GbgIntegrationException invalidResponse(String operation, String reason) {
        return new GbgIntegrationException(
            String.format("Invalid GBG API response during %s: %s", operation, reason));
    }
}
