package com.gumtree.tns.identityverification.service.impl;

import com.gumtree.tns.identityverification.dao.model.IdentityVerificationRequest;
import com.gumtree.tns.identityverification.dao.model.VerificationDocument;
import com.gumtree.tns.identityverification.dao.repository.IdentityVerificationRequestRepository;
import com.gumtree.tns.identityverification.dao.repository.VerificationDocumentRepository;
import com.gumtree.tns.identityverification.dto.DocumentUploadDto;
import com.gumtree.tns.identityverification.dto.DocumentResponseDto;
import com.gumtree.tns.identityverification.exception.DocumentUploadException;
import com.gumtree.tns.identityverification.exception.VerificationNotFoundException;
import com.gumtree.tns.identityverification.gateway.GbgGateway;
import com.gumtree.tns.identityverification.gateway.dto.GbgDocumentDto;
import com.gumtree.tns.identityverification.service.AuditLogService;
import com.gumtree.tns.identityverification.service.DocumentService;
import com.gumtree.tns.identityverification.service.mapper.VerificationMapper;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.RequiredArgsConstructor;
import lombok.extern.flogger.Flogger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Implementation of DocumentService
 */
@Flogger
@Service
@RequiredArgsConstructor
public class DocumentServiceImpl implements DocumentService {

    private final VerificationDocumentRepository documentRepository;
    private final IdentityVerificationRequestRepository verificationRequestRepository;
    private final GbgGateway gbgGateway;
    private final AuditLogService auditLogService;
    private final VerificationMapper verificationMapper;
    private final MeterRegistry meterRegistry;

    // Configuration
    private static final long MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
    private static final List<String> ALLOWED_MIME_TYPES = Arrays.asList(
        "image/jpeg", "image/png", "application/pdf"
    );

    // Metrics
    private final Counter documentUploadCounter;
    private final Timer documentUploadTimer;

    public DocumentServiceImpl(
            VerificationDocumentRepository documentRepository,
            IdentityVerificationRequestRepository verificationRequestRepository,
            GbgGateway gbgGateway,
            AuditLogService auditLogService,
            VerificationMapper verificationMapper,
            MeterRegistry meterRegistry) {
        this.documentRepository = documentRepository;
        this.verificationRequestRepository = verificationRequestRepository;
        this.gbgGateway = gbgGateway;
        this.auditLogService = auditLogService;
        this.verificationMapper = verificationMapper;
        this.meterRegistry = meterRegistry;
        
        // Initialize metrics
        this.documentUploadCounter = Counter.builder("documents.upload.total")
            .description("Total number of document uploads")
            .register(meterRegistry);
        
        this.documentUploadTimer = Timer.builder("documents.upload.duration")
            .description("Duration of document uploads")
            .register(meterRegistry);
    }

    @Override
    @Transactional
    public DocumentResponseDto uploadDocument(UUID verificationId, DocumentUploadDto documentDto) {
        log.atInfo().log("Uploading document for verification: %s", verificationId);
        
        try {
            return documentUploadTimer.recordCallable(() -> {
            try {
                // TODO: Fix counter increment call
                // documentUploadCounter.increment("type", documentDto.getDocumentType());
                
                // Validate document
                if (!validateDocument(documentDto)) {
                    throw DocumentUploadException.invalidFileType(
                        documentDto.getFileName(), 
                        documentDto.getMimeType(), 
                        ALLOWED_MIME_TYPES.toArray(new String[0])
                    );
                }
                
                // Get verification request
                IdentityVerificationRequest verificationRequest = verificationRequestRepository
                    .findById(verificationId)
                    .orElseThrow(() -> VerificationNotFoundException.forId(verificationId.toString()));
                
                // Create document entity
                VerificationDocument document = VerificationDocument.builder()
                    .verificationRequest(verificationRequest)
                    .documentType(VerificationDocument.DocumentType.valueOf(documentDto.getDocumentType()))
                    .documentStatus(VerificationDocument.DocumentStatus.UPLOADED)
                    .fileName(documentDto.getFileName())
                    .fileSize(documentDto.getFileSize())
                    .mimeType(documentDto.getMimeType())
                    .description(documentDto.getDescription())
                    .createdAt(LocalDateTime.now())
                    .updatedAt(LocalDateTime.now())
                    .build();
                
                // Save document
                document = documentRepository.save(document);
                
                // Upload to GBG
                try {
                    GbgDocumentDto gbgDocumentDto = GbgDocumentDto.builder()
                        .documentType(documentDto.getDocumentType())
                        .fileContent(documentDto.getFileContent())
                        .fileName(documentDto.getFileName())
                        .mimeType(documentDto.getMimeType())
                        .build();
                    
                    String gbgDocumentId = gbgGateway.uploadDocument(
                        verificationRequest.getGbgProfileId(), gbgDocumentDto);
                    
                    document.setGbgDocumentId(gbgDocumentId);
                    document.setDocumentStatus(VerificationDocument.DocumentStatus.PROCESSING);
                    document = documentRepository.save(document);
                    
                } catch (Exception e) {
                    log.atWarning().withCause(e).log("Failed to upload document to GBG: %s", document.getId());
                    // Continue with local storage, mark as failed
                    document.setDocumentStatus(VerificationDocument.DocumentStatus.REJECTED);
                    document = documentRepository.save(document);
                }
                
                // Log audit event
                auditLogService.logDocumentUploaded(document);
                
                log.atInfo().log("Document uploaded successfully: %s", document.getId());
                
                return verificationMapper.toDocumentResponseDto(document);
                
            } catch (Exception e) {
                // TODO: Fix counter increment call
                // Counter.builder("documents.upload.failed.total")
                //     .description("Total number of failed document uploads")
                //     .register(meterRegistry)
                //     .increment("type", documentDto.getDocumentType());
                
                log.atSevere().withCause(e).log("Failed to upload document for verification: %s", verificationId);
                throw e;
            }
        });
        } catch (Exception e) {
            log.atSevere().withCause(e).log("Failed to record timer for document upload");
            throw new RuntimeException("Failed to upload document", e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<DocumentResponseDto> getDocument(UUID documentId) {
        log.atInfo().log("Getting document: %s", documentId);
        
        Optional<VerificationDocument> document = documentRepository.findById(documentId);
        return document.map(verificationMapper::toDocumentResponseDto);
    }

    @Override
    @Transactional(readOnly = true)
    public List<DocumentResponseDto> getDocumentsForVerification(UUID verificationId) {
        log.atInfo().log("Getting documents for verification: %s", verificationId);
        
        List<VerificationDocument> documents = documentRepository.findByVerificationRequestId(verificationId);
        return documents.stream()
            .map(verificationMapper::toDocumentResponseDto)
            .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public boolean deleteDocument(UUID documentId) {
        log.atInfo().log("Deleting document: %s", documentId);
        
        Optional<VerificationDocument> documentOpt = documentRepository.findById(documentId);
        if (documentOpt.isPresent()) {
            VerificationDocument document = documentOpt.get();
            
            // Delete from GBG if exists
            if (document.getGbgDocumentId() != null) {
                try {
                    // GBG doesn't typically support document deletion
                    log.atInfo().log("Document exists in GBG but deletion not supported: %s", 
                        document.getGbgDocumentId());
                } catch (Exception e) {
                    log.atWarning().withCause(e).log("Failed to delete document from GBG: %s", 
                        document.getGbgDocumentId());
                }
            }
            
            // Delete from database
            documentRepository.delete(document);
            
            // Log audit event
            auditLogService.logDocumentDeleted(document);
            
            return true;
        }
        
        return false;
    }

    @Override
    @Transactional
    public DocumentResponseDto updateDocumentStatus(UUID documentId, String status) {
        log.atInfo().log("Updating document status: %s to %s", documentId, status);
        
        VerificationDocument document = documentRepository.findById(documentId)
            .orElseThrow(() -> new IllegalArgumentException("Document not found: " + documentId));
        
        VerificationDocument.DocumentStatus oldStatus = document.getDocumentStatus();
        document.setDocumentStatus(VerificationDocument.DocumentStatus.valueOf(status));
        document.setUpdatedAt(LocalDateTime.now());
        
        document = documentRepository.save(document);
        
        // Log audit event
        auditLogService.logDocumentStatusChanged(document, oldStatus, document.getDocumentStatus());
        
        return verificationMapper.toDocumentResponseDto(document);
    }

    @Override
    public boolean validateDocument(DocumentUploadDto documentDto) {
        // Check file size
        if (documentDto.getFileSize() != null && documentDto.getFileSize() > MAX_FILE_SIZE) {
            return false;
        }
        
        // Check MIME type
        if (!ALLOWED_MIME_TYPES.contains(documentDto.getMimeType())) {
            return false;
        }
        
        // Check file content is not empty
        if (documentDto.getFileContent() == null || documentDto.getFileContent().length == 0) {
            return false;
        }
        
        return true;
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<String> getDocumentDownloadUrl(UUID documentId) {
        log.atInfo().log("Getting download URL for document: %s", documentId);
        
        Optional<VerificationDocument> documentOpt = documentRepository.findById(documentId);
        if (documentOpt.isPresent()) {
            VerificationDocument document = documentOpt.get();
            
            // Try to get URL from GBG first
            if (document.getGbgDocumentId() != null) {
                try {
                    Optional<GbgDocumentDto> gbgDocument = gbgGateway.getDocument(document.getGbgDocumentId());
                    if (gbgDocument.isPresent()) {
                        return Optional.ofNullable(gbgDocument.get().getDownloadUrl());
                    }
                } catch (Exception e) {
                    log.atWarning().withCause(e).log("Failed to get download URL from GBG: %s", 
                        document.getGbgDocumentId());
                }
            }
            
            // Fallback to local URL
            return Optional.ofNullable(document.getDocumentUrl());
        }
        
        return Optional.empty();
    }
}
