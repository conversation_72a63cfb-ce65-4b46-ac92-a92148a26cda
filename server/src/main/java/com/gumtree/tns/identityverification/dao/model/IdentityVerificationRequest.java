package com.gumtree.tns.identityverification.dao.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

import com.gumtree.tns.identityverification.model.VerificationType;

@Entity
@Table(name = "identity_verification_requests")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IdentityVerificationRequest {

    @Id
    @GeneratedValue(generator = "UUID")
    @GenericGenerator(name = "UUID", strategy = "org.hibernate.id.UUIDGenerator")
    @Column(name = "id", updatable = false, nullable = false)
    private UUID id;

    @Column(name = "user_id")
    private Long userId;

    @Column(name = "account_id")
    private Long accountId;

    @Enumerated(EnumType.STRING)
    @Column(name = "verification_type", nullable = false)
    private VerificationType verificationType;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private VerificationStatus status;

    @Column(name = "risk_score")
    private Integer riskScore;

    @Enumerated(EnumType.STRING)
    @Column(name = "risk_level")
    private RiskLevel riskLevel;

    @Column(name = "gbg_profile_id")
    private String gbgProfileId;

    @Column(name = "customer_reference")
    private String customerReference;

    @Type(type = "jsonb")
    @Column(name = "personal_info", columnDefinition = "jsonb")
    private String personalInfo;

    @Type(type = "jsonb")
    @Column(name = "business_info", columnDefinition = "jsonb")
    private String businessInfo;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    @Column(name = "completed_at")
    private LocalDateTime completedAt;

    // Relationships
    @OneToMany(mappedBy = "verificationRequest", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<VerificationStep> verificationSteps;

    @OneToMany(mappedBy = "verificationRequest", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<VerificationDocument> verificationDocuments;

    @OneToMany(mappedBy = "verificationRequest", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Representative> representatives;

    @OneToMany(mappedBy = "verificationRequest", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<RiskAssessment> riskAssessments;

    // Helper methods
    public boolean isCompleted() {
        return VerificationStatus.COMPLETED.equals(this.status) ||
               VerificationStatus.APPROVED.equals(this.status) ||
               VerificationStatus.REJECTED.equals(this.status) ||
               VerificationStatus.EXPIRED.equals(this.status);
    }



    // Helper methods
    public boolean isKyc() {
        return VerificationType.KYC.equals(this.verificationType);
    }

    public boolean isKyb() {
        return VerificationType.KYB.equals(this.verificationType);
    }



    public boolean isInProgress() {
        return VerificationStatus.IN_PROGRESS.equals(this.status) ||
               VerificationStatus.UNDER_REVIEW.equals(this.status);
    }

    // Manual getters for compilation compatibility
    public UUID getId() {
        return id;
    }

    public VerificationType getVerificationType() {
        return verificationType;
    }

    public Integer getRiskScore() {
        return riskScore;
    }

    public VerificationStatus getStatus() {
        return status;
    }
}
