package com.gumtree.tns.identityverification.gateway.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * DTO for GBG Verification data
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GbgVerificationDto {

    private String id;

    private String profileId;

    private String representativeId;

    private String type;

    private String status;

    private String result;

    private Integer score;

    private Map<String, Object> details;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    private LocalDateTime completedAt;

    private Map<String, Object> metadata;
}
