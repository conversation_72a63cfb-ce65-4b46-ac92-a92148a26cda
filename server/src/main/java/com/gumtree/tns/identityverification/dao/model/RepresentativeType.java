package com.gumtree.tns.identityverification.dao.model;

/**
 * Enumeration for representative types as per GBG classification
 */
public enum RepresentativeType {
    /**
     * Company director
     */
    DIRECTOR,
    
    /**
     * Identity representative
     */
    IDENTITY,
    
    /**
     * Ownership representative
     */
    OWNERSHIP,
    
    /**
     * Declaratory representative
     */
    DECLARATORY,
    
    /**
     * Principal representative
     */
    PRINCIPAL,
    
    /**
     * Director company representative
     */
    DIRECTOR_COMPANY
}
