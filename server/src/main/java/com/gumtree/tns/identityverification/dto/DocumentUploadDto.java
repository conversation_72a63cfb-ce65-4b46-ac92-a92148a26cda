package com.gumtree.tns.identityverification.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * DTO for document upload request
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocumentUploadDto {

    @NotBlank
    private String documentType;

    @NotNull
    private byte[] fileContent;

    @NotBlank
    private String fileName;

    @NotBlank
    private String mimeType;

    private String description;

    private Long fileSize;

    private String checksum;
}
