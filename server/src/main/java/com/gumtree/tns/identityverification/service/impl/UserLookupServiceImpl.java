package com.gumtree.tns.identityverification.service.impl;

import com.gumtree.tns.identityverification.service.UserLookupService;
import lombok.RequiredArgsConstructor;
import lombok.extern.flogger.Flogger;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * Implementation of UserLookupService
 * This is a mock implementation - in production this would integrate with actual user management systems
 */
@Flogger
@Service
@RequiredArgsConstructor
public class UserLookupServiceImpl implements UserLookupService {

    @Override
    public List<Long> findUserIdsByEmail(String email) {
        log.atInfo().log("Looking up user IDs for email: %s", email);
        
        // Mock implementation - in production this would query user management system
        // For testing purposes, return a mock user ID based on email
        if (email != null && email.contains("@")) {
            // Extract a mock user ID from email hash
            long mockUserId = Math.abs(email.hashCode()) % 100000L + 10000L;
            return Arrays.asList(mockUserId);
        }
        
        return Arrays.asList();
    }

    @Override
    public List<Long> findAccountIdsByEmail(String email) {
        log.atInfo().log("Looking up account IDs for email: %s", email);
        
        // Mock implementation - in production this would query account management system
        // For testing purposes, return a mock account ID based on email
        if (email != null && email.contains("@")) {
            // Extract a mock account ID from email hash
            long mockAccountId = Math.abs(email.hashCode()) % 50000L + 50000L;
            return Arrays.asList(mockAccountId);
        }
        
        return Arrays.asList();
    }

    @Override
    public Optional<UserDetails> getUserDetails(Long userId) {
        log.atInfo().log("Getting user details for userId: %d", userId);
        
        // Mock implementation
        if (userId != null && userId > 0) {
            UserDetails userDetails = new UserDetails(
                userId,
                "user" + userId + "@example.com",
                "User",
                "Name" + userId,
                "+***********",
                true
            );
            return Optional.of(userDetails);
        }
        
        return Optional.empty();
    }

    @Override
    public Optional<AccountDetails> getAccountDetails(Long accountId) {
        log.atInfo().log("Getting account details for accountId: %d", accountId);
        
        // Mock implementation
        if (accountId != null && accountId > 0) {
            AccountDetails accountDetails = new AccountDetails(
                accountId,
                "account" + accountId + "@example.com",
                "Company " + accountId + " Ltd",
                "Contact Person",
                "+***********",
                true
            );
            return Optional.of(accountDetails);
        }
        
        return Optional.empty();
    }

    @Override
    public boolean userExists(Long userId) {
        log.atInfo().log("Checking if user exists: %d", userId);
        
        // Mock implementation - assume all positive IDs exist
        return userId != null && userId > 0;
    }

    @Override
    public boolean accountExists(Long accountId) {
        log.atInfo().log("Checking if account exists: %d", accountId);
        
        // Mock implementation - assume all positive IDs exist
        return accountId != null && accountId > 0;
    }

    @Override
    public Optional<String> getUserEmail(Long userId) {
        log.atInfo().log("Getting email for userId: %d", userId);
        
        // Mock implementation
        if (userId != null && userId > 0) {
            return Optional.of("user" + userId + "@example.com");
        }
        
        return Optional.empty();
    }

    @Override
    public Optional<String> getAccountEmail(Long accountId) {
        log.atInfo().log("Getting email for accountId: %d", accountId);
        
        // Mock implementation
        if (accountId != null && accountId > 0) {
            return Optional.of("account" + accountId + "@example.com");
        }
        
        return Optional.empty();
    }
}
