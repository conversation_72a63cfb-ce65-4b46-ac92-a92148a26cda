package com.gumtree.tns.identityverification.dao.repository;

import com.gumtree.tns.identityverification.dao.model.VerificationStep;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

/**
 * Repository interface for VerificationStep entity
 */
@Repository
public interface VerificationStepRepository extends JpaRepository<VerificationStep, UUID> {

    /**
     * Find verification steps by verification request ID
     */
    List<VerificationStep> findByVerificationRequestId(UUID verificationRequestId);

    /**
     * Find verification steps by verification request ID and step type
     */
    List<VerificationStep> findByVerificationRequestIdAndStepType(UUID verificationRequestId, VerificationStep.StepType stepType);

    /**
     * Find verification steps by verification request ID and step status
     */
    List<VerificationStep> findByVerificationRequestIdAndStepStatus(UUID verificationRequestId, VerificationStep.StepStatus stepStatus);

    /**
     * Find verification steps by GBG verification ID
     */
    List<VerificationStep> findByGbgVerificationId(String gbgVerificationId);

    /**
     * Delete verification steps by verification request ID
     */
    void deleteByVerificationRequestId(UUID verificationRequestId);
}
