package com.gumtree.tns.identityverification.exception;

import com.gumtree.tns.identityverification.dao.model.VerificationStatus;

/**
 * Exception thrown when a verification request is in an invalid state for the requested operation
 */
public class InvalidVerificationStateException extends RuntimeException {

    public InvalidVerificationStateException(String message) {
        super(message);
    }

    public InvalidVerificationStateException(String message, Throwable cause) {
        super(message, cause);
    }

    public static InvalidVerificationStateException forStatus(VerificationStatus currentStatus, String operation) {
        return new InvalidVerificationStateException(
            String.format("Cannot perform operation '%s' on verification with status '%s'", operation, currentStatus));
    }

    public static InvalidVerificationStateException forTransition(VerificationStatus from, VerificationStatus to) {
        return new InvalidVerificationStateException(
            String.format("Invalid status transition from '%s' to '%s'", from, to));
    }

    public static InvalidVerificationStateException expired(String verificationId) {
        return new InvalidVerificationStateException(
            "Verification request has expired: " + verificationId);
    }
}
