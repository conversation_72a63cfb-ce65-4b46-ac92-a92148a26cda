package com.gumtree.tns.identityverification.exception;

/**
 * Exception thrown when there's an error processing GDPR requests
 */
public class GdprProcessingException extends RuntimeException {

    public GdprProcessingException(String message) {
        super(message);
    }

    public GdprProcessingException(String message, Throwable cause) {
        super(message, cause);
    }

    public static GdprProcessingException sarFailed(String requestId, String email, Throwable cause) {
        return new GdprProcessingException(
            String.format("Failed to process Subject Access Request %s for email %s", requestId, email), cause);
    }

    public static GdprProcessingException ddrFailed(String requestId, String email, Throwable cause) {
        return new GdprProcessingException(
            String.format("Failed to process Data Deletion Request %s for email %s", requestId, email), cause);
    }

    public static GdprProcessingException invalidRequest(String requestId, String reason) {
        return new GdprProcessingException(
            String.format("Invalid GDPR request %s: %s", requestId, reason));
    }

    public static GdprProcessingException dataExportFailed(String email, Throwable cause) {
        return new GdprProcessingException("Failed to export data for email: " + email, cause);
    }
}
