package com.gumtree.tns.identityverification.gateway.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * DTO for GBG Representative data
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GbgRepresentativeDto {

    private String id;

    private String profileId;

    private String type;

    private String status;

    private Map<String, Object> personalInfo;

    private String position;

    private String shareholding;

    private Boolean isSignatory;

    private Boolean isPep;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    private Map<String, Object> metadata;
}
