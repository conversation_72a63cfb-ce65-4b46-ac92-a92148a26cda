package com.gumtree.tns.identityverification.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * DTO for address information
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AddressDto {

    private String street;

    private String street2;

    private String city;

    private String state;

    private String postalCode;

    @NotBlank
    private String countryCode;

    private String region;

    private String buildingNumber;

    private String buildingName;

    private String apartmentNumber;
}
