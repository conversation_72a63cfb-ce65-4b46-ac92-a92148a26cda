package com.gumtree.tns.identityverification.gateway.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * DTO for GBG Profile data
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GbgProfileDto {

    private String id;

    private String status;

    private String type;

    private String customerReference;

    private Map<String, Object> personalInfo;

    private Map<String, Object> businessInfo;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    private String domain;

    private Map<String, Object> metadata;
}
