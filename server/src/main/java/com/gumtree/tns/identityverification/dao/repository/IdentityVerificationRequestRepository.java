package com.gumtree.tns.identityverification.dao.repository;

import com.gumtree.tns.identityverification.dao.model.IdentityVerificationRequest;
import com.gumtree.tns.identityverification.dao.model.VerificationStatus;
import com.gumtree.tns.identityverification.model.VerificationType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository interface for IdentityVerificationRequest entity
 */
@Repository
public interface IdentityVerificationRequestRepository extends JpaRepository<IdentityVerificationRequest, UUID> {

    /**
     * Find verification request by user ID and status
     */
    Optional<IdentityVerificationRequest> findByUserIdAndStatusIn(Long userId, List<VerificationStatus> statuses);

    /**
     * Find verification request by account ID and status
     */
    Optional<IdentityVerificationRequest> findByAccountIdAndStatusIn(Long accountId, List<VerificationStatus> statuses);

    /**
     * Find latest verification request by user ID
     */
    Optional<IdentityVerificationRequest> findTopByUserIdOrderByCreatedAtDesc(Long userId);

    /**
     * Find latest verification request by account ID
     */
    Optional<IdentityVerificationRequest> findTopByAccountIdOrderByCreatedAtDesc(Long accountId);

    /**
     * Find verification requests by GBG profile ID
     */
    Optional<IdentityVerificationRequest> findByGbgProfileId(String gbgProfileId);

    /**
     * Find verification requests by customer reference
     */
    List<IdentityVerificationRequest> findByCustomerReference(String customerReference);

    /**
     * Find verification requests by verification type
     */
    Page<IdentityVerificationRequest> findByVerificationType(VerificationType verificationType, Pageable pageable);

    /**
     * Find verification requests by status
     */
    Page<IdentityVerificationRequest> findByStatus(VerificationStatus status, Pageable pageable);

    /**
     * Find verification requests created after a specific date
     */
    List<IdentityVerificationRequest> findByCreatedAtAfter(LocalDateTime createdAt);

    /**
     * Find expired verification requests
     */
    @Query("SELECT v FROM IdentityVerificationRequest v WHERE v.createdAt < :expiryDate AND v.status NOT IN :completedStatuses")
    List<IdentityVerificationRequest> findExpiredVerifications(
        @Param("expiryDate") LocalDateTime expiryDate,
        @Param("completedStatuses") List<VerificationStatus> completedStatuses);

    /**
     * Find verification requests with filters for admin review
     */
    @Query("SELECT v FROM IdentityVerificationRequest v WHERE " +
           "(:status IS NULL OR v.status = :status) AND " +
           "(:userId IS NULL OR v.userId = :userId) AND " +
           "(:accountId IS NULL OR v.accountId = :accountId)")
    Page<IdentityVerificationRequest> findByFilters(
        @Param("status") VerificationStatus status,
        @Param("userId") Long userId,
        @Param("accountId") Long accountId,
        Pageable pageable);

    /**
     * Count verification requests by status
     */
    long countByStatus(VerificationStatus status);

    /**
     * Count verification requests by verification type
     */
    long countByVerificationType(VerificationType verificationType);

    /**
     * Find verification requests by risk level
     */
    @Query("SELECT v FROM IdentityVerificationRequest v WHERE v.riskLevel = :riskLevel")
    List<IdentityVerificationRequest> findByRiskLevel(@Param("riskLevel") String riskLevel);

    /**
     * Find verification requests with high risk scores
     */
    @Query("SELECT v FROM IdentityVerificationRequest v WHERE v.riskScore >= :minRiskScore")
    List<IdentityVerificationRequest> findByRiskScoreGreaterThanEqual(@Param("minRiskScore") Integer minRiskScore);

    /**
     * Find verification requests for a specific date range
     */
    @Query("SELECT v FROM IdentityVerificationRequest v WHERE v.createdAt BETWEEN :startDate AND :endDate")
    List<IdentityVerificationRequest> findByDateRange(
        @Param("startDate") LocalDateTime startDate,
        @Param("endDate") LocalDateTime endDate);

    /**
     * Find verification requests by user ID
     */
    List<IdentityVerificationRequest> findByUserId(Long userId);

    /**
     * Find verification requests by account ID
     */
    List<IdentityVerificationRequest> findByAccountId(Long accountId);

    /**
     * Count verification requests by user ID
     */
    long countByUserId(Long userId);

    /**
     * Count verification requests by account ID
     */
    long countByAccountId(Long accountId);

    /**
     * Find verification requests created before a specific date
     */
    List<IdentityVerificationRequest> findByCreatedAtBefore(LocalDateTime createdAt);
}
