package com.gumtree.tns.identityverification.gateway;

import com.gumtree.tns.identityverification.dao.model.IdentityVerificationRequest;
import com.gumtree.tns.identityverification.gateway.dto.GbgProfileDto;
import com.gumtree.tns.identityverification.gateway.dto.GbgRepresentativeDto;
import com.gumtree.tns.identityverification.gateway.dto.GbgVerificationDto;
import com.gumtree.tns.identityverification.gateway.dto.GbgDocumentDto;

import java.util.List;
import java.util.Optional;

/**
 * Gateway interface for GBG API integration
 */
public interface GbgGateway {

    /**
     * Create a new profile in GBG
     *
     * @param verificationRequest the verification request
     * @return GBG profile ID
     */
    String createProfile(IdentityVerificationRequest verificationRequest);

    /**
     * Get profile details from GBG
     *
     * @param gbgProfileId the GBG profile ID
     * @return profile details
     */
    Optional<GbgProfileDto> getProfile(String gbgProfileId);

    /**
     * Update profile in GBG
     *
     * @param gbgProfileId the GBG profile ID
     * @param verificationRequest the verification request
     * @return updated profile
     */
    GbgProfileDto updateProfile(String gbgProfileId, IdentityVerificationRequest verificationRequest);

    /**
     * Create a representative for a profile
     *
     * @param gbgProfileId the GBG profile ID
     * @param representativeData the representative data
     * @return GBG representative ID
     */
    String createRepresentative(String gbgProfileId, GbgRepresentativeDto representativeData);

    /**
     * Get representatives for a profile
     *
     * @param gbgProfileId the GBG profile ID
     * @return list of representatives
     */
    List<GbgRepresentativeDto> getRepresentatives(String gbgProfileId);

    /**
     * Get representative verifications
     *
     * @param gbgRepresentativeId the GBG representative ID
     * @return list of verifications
     */
    List<GbgVerificationDto> getRepresentativeVerifications(String gbgRepresentativeId);

    /**
     * Upload document to GBG
     *
     * @param gbgProfileId the GBG profile ID
     * @param documentData the document data
     * @return GBG document ID
     */
    String uploadDocument(String gbgProfileId, GbgDocumentDto documentData);

    /**
     * Get document details from GBG
     *
     * @param gbgDocumentId the GBG document ID
     * @return document details
     */
    Optional<GbgDocumentDto> getDocument(String gbgDocumentId);

    /**
     * Get all documents for a profile
     *
     * @param gbgProfileId the GBG profile ID
     * @return list of documents
     */
    List<GbgDocumentDto> getProfileDocuments(String gbgProfileId);

    /**
     * Submit verification request to GBG
     *
     * @param verificationRequest the verification request
     * @return verification result
     */
    GbgVerificationDto submitVerification(IdentityVerificationRequest verificationRequest);

    /**
     * Get verification status from GBG
     *
     * @param gbgVerificationId the GBG verification ID
     * @return verification status
     */
    Optional<GbgVerificationDto> getVerificationStatus(String gbgVerificationId);

    /**
     * Search for entities in GBG
     *
     * @param searchCriteria the search criteria
     * @return search results
     */
    List<GbgProfileDto> searchProfiles(String searchCriteria);

    /**
     * Get profile domain information
     *
     * @param gbgProfileId the GBG profile ID
     * @return domain information
     */
    Optional<String> getProfileDomain(String gbgProfileId);

    /**
     * Create profile domain
     *
     * @param gbgProfileId the GBG profile ID
     * @param domain the domain
     * @return success status
     */
    boolean createProfileDomain(String gbgProfileId, String domain);

    /**
     * Get profile analysis
     *
     * @param gbgProfileId the GBG profile ID
     * @return analysis results
     */
    List<String> getProfileAnalysis(String gbgProfileId);

    /**
     * Get profile checklists
     *
     * @param gbgProfileId the GBG profile ID
     * @return checklist results
     */
    List<String> getProfileChecklists(String gbgProfileId);

    /**
     * Get profile forms
     *
     * @param gbgProfileId the GBG profile ID
     * @return form results
     */
    List<String> getProfileForms(String gbgProfileId);
}
