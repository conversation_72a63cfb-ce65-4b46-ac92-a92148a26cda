package com.gumtree.tns.identityverification.exception;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.RequiredArgsConstructor;
import lombok.extern.flogger.Flogger;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;

import javax.validation.ConstraintViolationException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Global exception handler for the Identity Verification Service
 */
@Flogger
@RestControllerAdvice
@RequiredArgsConstructor
public class GlobalExceptionHandler {

    private final MeterRegistry meterRegistry;

    // Metrics
    private final Counter exceptionCounter;

    public GlobalExceptionHandler(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        
        // Initialize metrics
        this.exceptionCounter = Counter.builder("identity.verification.exceptions.total")
            .description("Total number of exceptions in identity verification service")
            .register(meterRegistry);
    }

    @ExceptionHandler(VerificationNotFoundException.class)
    public ResponseEntity<ApiErrorResponse> handleVerificationNotFoundException(
            VerificationNotFoundException ex, WebRequest request) {
        
        log.atWarning().log("Verification not found: %s", ex.getMessage());
        Counter.builder("identity.verification.exceptions.total")
            .tag("type", "verification_not_found")
            .register(meterRegistry)
            .increment();
        
        ApiErrorResponse errorResponse = ApiErrorResponse.builder()
            .timestamp(LocalDateTime.now())
            .status(HttpStatus.NOT_FOUND.value())
            .error("Verification Not Found")
            .message(ex.getMessage())
            .path(request.getDescription(false))
            .build();
        
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorResponse);
    }

    @ExceptionHandler(InvalidVerificationStateException.class)
    public ResponseEntity<ApiErrorResponse> handleInvalidVerificationStateException(
            InvalidVerificationStateException ex, WebRequest request) {
        
        log.atWarning().log("Invalid verification state: %s", ex.getMessage());
        Counter.builder("identity.verification.exceptions.total")
            .tag("type", "invalid_verification_state")
            .register(meterRegistry)
            .increment();
        
        ApiErrorResponse errorResponse = ApiErrorResponse.builder()
            .timestamp(LocalDateTime.now())
            .status(HttpStatus.BAD_REQUEST.value())
            .error("Invalid Verification State")
            .message(ex.getMessage())
            .path(request.getDescription(false))
            .build();
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
    }

    @ExceptionHandler(GbgIntegrationException.class)
    public ResponseEntity<ApiErrorResponse> handleGbgIntegrationException(
            GbgIntegrationException ex, WebRequest request) {
        
        log.atSevere().withCause(ex).log("GBG integration error: %s", ex.getMessage());
        Counter.builder("identity.verification.exceptions.total")
            .tag("type", "gbg_integration_error")
            .register(meterRegistry)
            .increment();
        
        ApiErrorResponse errorResponse = ApiErrorResponse.builder()
            .timestamp(LocalDateTime.now())
            .status(HttpStatus.SERVICE_UNAVAILABLE.value())
            .error("External Service Error")
            .message("Identity verification service is temporarily unavailable")
            .path(request.getDescription(false))
            .build();
        
        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(errorResponse);
    }

    @ExceptionHandler(DocumentUploadException.class)
    public ResponseEntity<ApiErrorResponse> handleDocumentUploadException(
            DocumentUploadException ex, WebRequest request) {
        
        log.atWarning().log("Document upload error: %s", ex.getMessage());
        Counter.builder("identity.verification.exceptions.total")
            .tag("type", "document_upload_error")
            .register(meterRegistry)
            .increment();
        
        ApiErrorResponse errorResponse = ApiErrorResponse.builder()
            .timestamp(LocalDateTime.now())
            .status(HttpStatus.BAD_REQUEST.value())
            .error("Document Upload Error")
            .message(ex.getMessage())
            .path(request.getDescription(false))
            .build();
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
    }

    @ExceptionHandler(GdprProcessingException.class)
    public ResponseEntity<ApiErrorResponse> handleGdprProcessingException(
            GdprProcessingException ex, WebRequest request) {
        
        log.atSevere().withCause(ex).log("GDPR processing error: %s", ex.getMessage());
        Counter.builder("identity.verification.exceptions.total")
            .tag("type", "gdpr_processing_error")
            .register(meterRegistry)
            .increment();
        
        ApiErrorResponse errorResponse = ApiErrorResponse.builder()
            .timestamp(LocalDateTime.now())
            .status(HttpStatus.INTERNAL_SERVER_ERROR.value())
            .error("GDPR Processing Error")
            .message("Failed to process GDPR request")
            .path(request.getDescription(false))
            .build();
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ApiErrorResponse> handleValidationExceptions(
            MethodArgumentNotValidException ex, WebRequest request) {
        
        log.atWarning().log("Validation error: %s", ex.getMessage());
        Counter.builder("identity.verification.exceptions.total")
            .tag("type", "validation_error")
            .register(meterRegistry)
            .increment();
        
        Map<String, String> errors = new HashMap<>();
        ex.getBindingResult().getAllErrors().forEach((error) -> {
            String fieldName = ((FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();
            errors.put(fieldName, errorMessage);
        });
        
        ApiErrorResponse errorResponse = ApiErrorResponse.builder()
            .timestamp(LocalDateTime.now())
            .status(HttpStatus.BAD_REQUEST.value())
            .error("Validation Failed")
            .message("Request validation failed")
            .path(request.getDescription(false))
            .validationErrors(errors)
            .build();
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
    }

    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<ApiErrorResponse> handleConstraintViolationException(
            ConstraintViolationException ex, WebRequest request) {
        
        log.atWarning().log("Constraint violation: %s", ex.getMessage());
        Counter.builder("identity.verification.exceptions.total")
            .tag("type", "constraint_violation")
            .register(meterRegistry)
            .increment();
        
        Map<String, String> errors = new HashMap<>();
        ex.getConstraintViolations().forEach(violation -> {
            String fieldName = violation.getPropertyPath().toString();
            String errorMessage = violation.getMessage();
            errors.put(fieldName, errorMessage);
        });
        
        ApiErrorResponse errorResponse = ApiErrorResponse.builder()
            .timestamp(LocalDateTime.now())
            .status(HttpStatus.BAD_REQUEST.value())
            .error("Constraint Violation")
            .message("Request constraint validation failed")
            .path(request.getDescription(false))
            .validationErrors(errors)
            .build();
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
    }

    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<ApiErrorResponse> handleIllegalArgumentException(
            IllegalArgumentException ex, WebRequest request) {
        
        log.atWarning().log("Illegal argument: %s", ex.getMessage());
        Counter.builder("identity.verification.exceptions.total")
            .tag("type", "illegal_argument")
            .register(meterRegistry)
            .increment();
        
        ApiErrorResponse errorResponse = ApiErrorResponse.builder()
            .timestamp(LocalDateTime.now())
            .status(HttpStatus.BAD_REQUEST.value())
            .error("Invalid Request")
            .message(ex.getMessage())
            .path(request.getDescription(false))
            .build();
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
    }

    @ExceptionHandler(IllegalStateException.class)
    public ResponseEntity<ApiErrorResponse> handleIllegalStateException(
            IllegalStateException ex, WebRequest request) {
        
        log.atWarning().log("Illegal state: %s", ex.getMessage());
        Counter.builder("identity.verification.exceptions.total")
            .tag("type", "illegal_state")
            .register(meterRegistry)
            .increment();
        
        ApiErrorResponse errorResponse = ApiErrorResponse.builder()
            .timestamp(LocalDateTime.now())
            .status(HttpStatus.CONFLICT.value())
            .error("Invalid State")
            .message(ex.getMessage())
            .path(request.getDescription(false))
            .build();
        
        return ResponseEntity.status(HttpStatus.CONFLICT).body(errorResponse);
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<ApiErrorResponse> handleGenericException(
            Exception ex, WebRequest request) {
        
        log.atSevere().withCause(ex).log("Unexpected error: %s", ex.getMessage());
        Counter.builder("identity.verification.exceptions.total")
            .tag("type", "generic_error")
            .register(meterRegistry)
            .increment();
        
        ApiErrorResponse errorResponse = ApiErrorResponse.builder()
            .timestamp(LocalDateTime.now())
            .status(HttpStatus.INTERNAL_SERVER_ERROR.value())
            .error("Internal Server Error")
            .message("An unexpected error occurred")
            .path(request.getDescription(false))
            .build();
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
    }
}
