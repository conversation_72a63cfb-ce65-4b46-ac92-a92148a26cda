package com.gumtree.tns.identityverification.dto;

import com.gumtree.tns.identityverification.dao.model.RiskLevel;
import com.gumtree.tns.identityverification.dao.model.VerificationStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * DTO for verification status response
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VerificationStatusResponseDto {

    private UUID verificationId;

    private Long userId;

    private Long accountId;

    private VerificationStatus status;

    private Integer riskScore;

    private RiskLevel riskLevel;

    private List<VerificationStepDto> completedSteps;

    private List<VerificationStepDto> nextSteps;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    private LocalDateTime completedAt;
}
