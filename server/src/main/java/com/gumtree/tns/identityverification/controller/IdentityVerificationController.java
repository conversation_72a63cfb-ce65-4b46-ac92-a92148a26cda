package com.gumtree.tns.identityverification.controller;

import com.gumtree.tns.identityverification.service.IdentityVerificationApi;
import com.gumtree.tns.identityverification.model.InitiateVerificationRequest;
import com.gumtree.tns.identityverification.model.InitiateBusinessVerificationRequest;
import com.gumtree.tns.identityverification.model.VerificationResponse;
import com.gumtree.tns.identityverification.model.VerificationStatusResponse;
import com.gumtree.tns.identityverification.model.DocumentUploadRequest;
import com.gumtree.tns.identityverification.model.DocumentResponse;
import com.gumtree.tns.identityverification.model.DocumentType;
import com.gumtree.tns.identityverification.service.IdentityVerificationService;
import com.gumtree.tns.identityverification.service.DocumentService;
import com.gumtree.tns.identityverification.service.mapper.VerificationControllerMapper;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.RequiredArgsConstructor;
import lombok.extern.flogger.Flogger;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.UUID;

/**
 * REST Controller for Identity Verification operations
 */
@Flogger
@RestController
public class IdentityVerificationController implements IdentityVerificationApi {

    private final IdentityVerificationService identityVerificationService;
    private final DocumentService documentService;
    private final VerificationControllerMapper controllerMapper;
    private final MeterRegistry meterRegistry;

    // Metrics
    private final Counter verificationRequestsCounter;
    private final Timer verificationRequestsTimer;

    public IdentityVerificationController(
            IdentityVerificationService identityVerificationService,
            DocumentService documentService,
            VerificationControllerMapper controllerMapper,
            MeterRegistry meterRegistry) {
        this.identityVerificationService = identityVerificationService;
        this.documentService = documentService;
        this.controllerMapper = controllerMapper;
        this.meterRegistry = meterRegistry;
        
        // Initialize metrics
        this.verificationRequestsCounter = Counter.builder("identity.verification.requests.total")
            .description("Total number of identity verification requests")
            .register(meterRegistry);
        
        this.verificationRequestsTimer = Timer.builder("identity.verification.requests.duration")
            .description("Duration of identity verification requests")
            .register(meterRegistry);
    }

    @Override
    public ResponseEntity<VerificationResponse> initiateUserIdentityVerification(
            @PathVariable("user-id") Long userId,
            @Valid @RequestBody InitiateVerificationRequest request) {
        
        log.atInfo().log("Initiating user identity verification for userId: %d", userId);
        
        try {
            return verificationRequestsTimer.recordCallable(() -> {
            try {
                Counter.builder("verification.requests.total")
                    .tag("type", "user")
                    .tag("operation", "initiate")
                    .register(meterRegistry)
                    .increment();
                
                var requestDto = controllerMapper.toInitiateVerificationRequestDto(request);
                var responseDto = identityVerificationService.initiateUserVerification(userId, requestDto);
                var response = controllerMapper.toVerificationResponse(responseDto);
                
                log.atInfo().log("User identity verification initiated successfully for userId: %d, verificationId: %s", 
                    userId, response.getVerificationId());
                
                return ResponseEntity.status(HttpStatus.CREATED).body(response);
                
            } catch (Exception e) {
                log.atSevere().withCause(e).log("Failed to initiate user identity verification for userId: %d", userId);
                throw e;
            }
        });
        } catch (Exception e) {
            log.atSevere().withCause(e).log("Failed to record timer");
            throw new RuntimeException("Failed to process request", e);
        }
    }

    @Override
    public ResponseEntity<VerificationResponse> initiateAccountIdentityVerification(
            @PathVariable("account-id") Long accountId,
            @Valid @RequestBody InitiateBusinessVerificationRequest request) {
        
        log.atInfo().log("Initiating account identity verification for accountId: %d", accountId);
        
        try {
            return verificationRequestsTimer.recordCallable(() -> {
            try {
                Counter.builder("verification.requests.total")
                    .tag("type", "account")
                    .tag("operation", "initiate")
                    .register(meterRegistry)
                    .increment();
                
                var requestDto = controllerMapper.toInitiateVerificationRequestDto(request);
                var responseDto = identityVerificationService.initiateAccountVerification(accountId, requestDto);
                var response = controllerMapper.toVerificationResponse(responseDto);
                
                log.atInfo().log("Account identity verification initiated successfully for accountId: %d, verificationId: %s", 
                    accountId, response.getVerificationId());
                
                return ResponseEntity.status(HttpStatus.CREATED).body(response);
                
            } catch (Exception e) {
                log.atSevere().withCause(e).log("Failed to initiate account identity verification for accountId: %d", accountId);
                throw e;
            }
        });
        } catch (Exception e) {
            log.atSevere().withCause(e).log("Failed to record timer");
            throw new RuntimeException("Failed to process request", e);
        }
    }

    public ResponseEntity<VerificationStatusResponse> getUserVerificationStatus(@PathVariable("user-id") Long userId) {
        log.atInfo().log("Getting verification status for userId: %d", userId);
        
        try {
            return verificationRequestsTimer.recordCallable(() -> {
            try {
                // TODO: Fix counter increment call
                // verificationRequestsCounter.increment("type", "user", "operation", "get_status");
                
                var statusDto = identityVerificationService.getUserVerificationStatus(userId);
                
                if (statusDto.isPresent()) {
                    var response = controllerMapper.toVerificationStatusResponse(statusDto.get());
                    return ResponseEntity.ok(response);
                } else {
                    return ResponseEntity.notFound().build();
                }
                
            } catch (Exception e) {
                log.atSevere().withCause(e).log("Failed to get verification status for userId: %d", userId);
                throw e;
            }
        });
        } catch (Exception e) {
            log.atSevere().withCause(e).log("Failed to record timer");
            throw new RuntimeException("Failed to process request", e);
        }
    }

    public ResponseEntity<VerificationStatusResponse> getAccountVerificationStatus(@PathVariable("account-id") Long accountId) {
        log.atInfo().log("Getting verification status for accountId: %d", accountId);
        
        try {
            return verificationRequestsTimer.recordCallable(() -> {
            try {
                // TODO: Fix counter increment call
                // verificationRequestsCounter.increment("type", "account", "operation", "get_status");
                
                var statusDto = identityVerificationService.getAccountVerificationStatus(accountId);
                
                if (statusDto.isPresent()) {
                    var response = controllerMapper.toVerificationStatusResponse(statusDto.get());
                    return ResponseEntity.ok(response);
                } else {
                    return ResponseEntity.notFound().build();
                }
                
            } catch (Exception e) {
                log.atSevere().withCause(e).log("Failed to get verification status for accountId: %d", accountId);
                throw e;
            }
        });
        } catch (Exception e) {
            log.atSevere().withCause(e).log("Failed to record timer");
            throw new RuntimeException("Failed to process request", e);
        }
    }

    @Override
    public ResponseEntity<DocumentResponse> uploadVerificationDocument(
            @PathVariable("verification-id") UUID verificationId,
            @Valid @RequestPart(value = "documentType", required = true) DocumentType documentType,
            @RequestPart(value = "file", required = true) MultipartFile file,
            @Valid @RequestPart(value = "description", required = false) String description) {
        
        log.atInfo().log("Uploading document for verificationId: %s", verificationId);
        
        try {
            return verificationRequestsTimer.recordCallable(() -> {
            try {
                // TODO: Fix counter increment call
                // // TODO: Fix counter increment call
 // verificationRequestsCounter.increment("operation", "upload_document");
                
                var documentDto = controllerMapper.toDocumentUploadDto(documentType, file, description);
                var responseDto = documentService.uploadDocument(verificationId, documentDto);
                var response = controllerMapper.toDocumentResponse(responseDto);
                
                log.atInfo().log("Document uploaded successfully for verificationId: %s, documentId: %s", 
                    verificationId, response.getDocumentId());
                
                return ResponseEntity.status(HttpStatus.CREATED).body(response);
                
            } catch (Exception e) {
                log.atSevere().withCause(e).log("Failed to upload document for verificationId: %s", verificationId);
                throw e;
            }
        });
        } catch (Exception e) {
            log.atSevere().withCause(e).log("Failed to record timer");
            throw new RuntimeException("Failed to process request", e);
        }
    }

    @Override
    public ResponseEntity<VerificationResponse> submitVerificationRequest(@PathVariable("verification-id") UUID verificationId) {
        log.atInfo().log("Submitting verification request: %s", verificationId);
        
        try {
            return verificationRequestsTimer.recordCallable(() -> {
            try {
                Counter.builder("verification.requests.total")
                    .tag("operation", "submit")
                    .register(meterRegistry)
                    .increment();
                
                var responseDto = identityVerificationService.submitVerificationRequest(verificationId);
                var response = controllerMapper.toVerificationResponse(responseDto);
                
                log.atInfo().log("Verification request submitted successfully: %s", verificationId);
                
                return ResponseEntity.ok(response);
                
            } catch (Exception e) {
                log.atSevere().withCause(e).log("Failed to submit verification request: %s", verificationId);
                throw e;
            }
        });
        } catch (Exception e) {
            log.atSevere().withCause(e).log("Failed to record timer");
            throw new RuntimeException("Failed to process request", e);
        }
    }
}
