package com.gumtree.tns.identityverification.service;

import com.gumtree.tns.identityverification.dto.SarRequestDto;
import com.gumtree.tns.identityverification.dto.SarResponseDto;
import com.gumtree.tns.identityverification.dto.DdrRequestDto;
import com.gumtree.tns.identityverification.dto.DdrResponseDto;

/**
 * Service interface for GDPR compliance operations
 */
public interface GdprService {

    /**
     * Process Subject Access Request (SAR)
     * Retrieves all personal data for a given user email
     *
     * @param sarRequest the SAR request details
     * @return SAR response with user data
     */
    SarResponseDto processSubjectAccessRequest(SarRequestDto sarRequest);

    /**
     * Process Data Deletion Request (DDR)
     * Deletes all personal data for a given user email
     *
     * @param ddrRequest the DDR request details
     * @return DDR response with deletion status
     */
    DdrResponseDto processDataDeletionRequest(DdrRequestDto ddrRequest);

    /**
     * Check if user has any verification data
     *
     * @param email the user email
     * @return true if user has verification data
     */
    boolean hasUserVerificationData(String email);

    /**
     * Get user verification data count
     *
     * @param email the user email
     * @return count of verification records
     */
    long getUserVerificationDataCount(String email);

    /**
     * Anonymize user data instead of deletion
     * Used when data cannot be deleted due to legal requirements
     *
     * @param email the user email
     * @return true if anonymization was successful
     */
    boolean anonymizeUserData(String email);

    /**
     * Export user data to JSON format
     *
     * @param email the user email
     * @return JSON string with user data
     */
    String exportUserDataAsJson(String email);

    /**
     * Validate GDPR request
     *
     * @param requestId the request ID
     * @param email the user email
     * @return true if request is valid
     */
    boolean validateGdprRequest(String requestId, String email);
}
