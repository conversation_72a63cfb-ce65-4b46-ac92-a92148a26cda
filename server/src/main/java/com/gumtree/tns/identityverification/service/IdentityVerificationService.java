package com.gumtree.tns.identityverification.service;

import com.gumtree.tns.identityverification.dao.model.IdentityVerificationRequest;
import com.gumtree.tns.identityverification.dao.model.VerificationStatus;
import com.gumtree.tns.identityverification.model.VerificationType;
import com.gumtree.tns.identityverification.dto.InitiateVerificationRequestDto;
import com.gumtree.tns.identityverification.dto.VerificationResponseDto;
import com.gumtree.tns.identityverification.dto.VerificationStatusResponseDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Optional;
import java.util.UUID;

/**
 * Service interface for identity verification operations
 */
public interface IdentityVerificationService {

    /**
     * Initiate identity verification for a user (KYC)
     *
     * @param userId the user ID
     * @param request the verification request details
     * @return verification response
     */
    VerificationResponseDto initiateUserVerification(Long userId, InitiateVerificationRequestDto request);

    /**
     * Initiate identity verification for an account (KYB)
     *
     * @param accountId the account ID
     * @param request the verification request details
     * @return verification response
     */
    VerificationResponseDto initiateAccountVerification(Long accountId, InitiateVerificationRequestDto request);

    /**
     * Get verification status for a user
     *
     * @param userId the user ID
     * @return verification status response
     */
    Optional<VerificationStatusResponseDto> getUserVerificationStatus(Long userId);

    /**
     * Get verification status for an account
     *
     * @param accountId the account ID
     * @return verification status response
     */
    Optional<VerificationStatusResponseDto> getAccountVerificationStatus(Long accountId);

    /**
     * Get verification request by ID
     *
     * @param verificationId the verification request ID
     * @return verification status response
     */
    Optional<VerificationStatusResponseDto> getVerificationStatus(UUID verificationId);

    /**
     * Submit verification request for processing
     *
     * @param verificationId the verification request ID
     * @return verification response
     */
    VerificationResponseDto submitVerificationRequest(UUID verificationId);

    /**
     * Update verification status
     *
     * @param verificationId the verification request ID
     * @param status the new status
     * @return updated verification request
     */
    IdentityVerificationRequest updateVerificationStatus(UUID verificationId, VerificationStatus status);

    /**
     * Get verification requests for admin review
     *
     * @param status filter by status
     * @param userId filter by user ID
     * @param accountId filter by account ID
     * @param pageable pagination parameters
     * @return page of verification requests
     */
    Page<VerificationStatusResponseDto> getVerificationRequestsForReview(
            VerificationStatus status, Long userId, Long accountId, Pageable pageable);

    /**
     * Process GBG webhook event
     *
     * @param eventType the event type
     * @param eventData the event data
     */
    void processGbgWebhookEvent(String eventType, String eventData);

    /**
     * Calculate risk score for verification request
     *
     * @param verificationId the verification request ID
     * @return calculated risk score
     */
    Integer calculateRiskScore(UUID verificationId);

    /**
     * Check if verification is expired
     *
     * @param verificationId the verification request ID
     * @return true if expired
     */
    boolean isVerificationExpired(UUID verificationId);

    /**
     * Get verification requests by type
     *
     * @param verificationType the verification type
     * @param pageable pagination parameters
     * @return page of verification requests
     */
    Page<VerificationStatusResponseDto> getVerificationRequestsByType(
            VerificationType verificationType, Pageable pageable);
}
