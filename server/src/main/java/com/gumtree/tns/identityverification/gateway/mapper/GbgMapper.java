package com.gumtree.tns.identityverification.gateway.mapper;

import com.gumtree.tns.identityverification.dao.model.IdentityVerificationRequest;
import com.gumtree.tns.identityverification.gateway.client.dto.*;
import com.gumtree.tns.identityverification.gateway.client.dto.GbgApiClientDtos.*;
import com.gumtree.tns.identityverification.gateway.dto.*;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper for GBG API data transformations
 */
@Component
public class GbgMapper {

    public GbgApiProfileRequest toGbgProfileCreationRequest(IdentityVerificationRequest verificationRequest) {
        return GbgApiProfileRequest.builder()
            .customerReference(verificationRequest.getCustomerReference())
            .type(verificationRequest.getVerificationType().name())
            .personalInfo(verificationRequest.getPersonalInfo())
            .businessInfo(verificationRequest.getBusinessInfo())
            .build();
    }

    public GbgApiProfileUpdateRequest toGbgProfileUpdateRequest(IdentityVerificationRequest verificationRequest) {
        return GbgApiProfileUpdateRequest.builder()
            .personalInfo(verificationRequest.getPersonalInfo())
            .businessInfo(verificationRequest.getBusinessInfo())
            .build();
    }

    public GbgApiRepresentativeRequest toGbgRepresentativeCreationRequest(GbgRepresentativeDto representativeData) {
        return GbgApiRepresentativeRequest.builder()
            .profileId(representativeData.getProfileId())
            .type(representativeData.getType())
            .personalInfo(representativeData.getPersonalInfo())
            .position(representativeData.getPosition())
            .shareholding(representativeData.getShareholding())
            .isSignatory(representativeData.getIsSignatory())
            .isPep(representativeData.getIsPep())
            .build();
    }

    public GbgApiDocumentRequest toGbgDocumentUploadRequest(GbgDocumentDto documentData) {
        return GbgApiDocumentRequest.builder()
            .profileId(documentData.getProfileId())
            .documentType(documentData.getDocumentType())
            .fileName(documentData.getFileName())
            .mimeType(documentData.getMimeType())
            .fileContent(documentData.getFileContent())
            .build();
    }

    public GbgApiVerificationRequest toGbgVerificationSubmissionRequest(IdentityVerificationRequest verificationRequest) {
        return GbgApiVerificationRequest.builder()
            .profileId(verificationRequest.getGbgProfileId())
            .type("FULL_VERIFICATION")
            .customerReference(verificationRequest.getCustomerReference())
            .build();
    }

    public GbgApiSearchRequest toGbgSearchRequest(String searchCriteria) {
        return GbgApiSearchRequest.builder()
            .query(searchCriteria)
            .limit(50)
            .build();
    }

    public GbgApiDomainRequest toGbgDomainCreationRequest(String domain) {
        return GbgApiDomainRequest.builder()
            .domain(domain)
            .build();
    }

    // Response mapping methods
    public GbgProfileDto toGbgProfileDto(GbgApiProfileResponse response) {
        return GbgProfileDto.builder()
            .id(response.getId())
            .status(response.getStatus())
            .type(response.getType())
            .customerReference(response.getCustomerReference())
            .personalInfo(response.getPersonalInfo())
            .businessInfo(response.getBusinessInfo())
            .createdAt(response.getCreatedAt())
            .updatedAt(response.getUpdatedAt())
            .domain(response.getDomain())
            .metadata(response.getMetadata())
            .build();
    }

    public List<GbgProfileDto> toGbgProfileDtoList(List<GbgApiProfileResponse> responses) {
        return responses.stream()
            .map(this::toGbgProfileDto)
            .collect(Collectors.toList());
    }

    public GbgRepresentativeDto toGbgRepresentativeDto(GbgApiRepresentativeResponse response) {
        return GbgRepresentativeDto.builder()
            .id(response.getId())
            .profileId(response.getProfileId())
            .type(response.getType())
            .status(response.getStatus())
            .personalInfo(response.getPersonalInfo())
            .position(response.getPosition())
            .shareholding(response.getShareholding())
            .isSignatory(response.getIsSignatory())
            .isPep(response.getIsPep())
            .createdAt(response.getCreatedAt())
            .updatedAt(response.getUpdatedAt())
            .metadata(response.getMetadata())
            .build();
    }

    public List<GbgRepresentativeDto> toGbgRepresentativeDtoList(List<GbgApiRepresentativeResponse> responses) {
        return responses.stream()
            .map(this::toGbgRepresentativeDto)
            .collect(Collectors.toList());
    }

    public GbgVerificationDto toGbgVerificationDto(GbgApiVerificationResponse response) {
        return GbgVerificationDto.builder()
            .id(response.getId())
            .profileId(response.getProfileId())
            .representativeId(response.getRepresentativeId())
            .type(response.getType())
            .status(response.getStatus())
            .result(response.getResult())
            .score(response.getScore())
            .details(response.getDetails())
            .createdAt(response.getCreatedAt())
            .updatedAt(response.getUpdatedAt())
            .completedAt(response.getCompletedAt())
            .metadata(response.getMetadata())
            .build();
    }

    public List<GbgVerificationDto> toGbgVerificationDtoList(List<GbgApiVerificationResponse> responses) {
        return responses.stream()
            .map(this::toGbgVerificationDto)
            .collect(Collectors.toList());
    }

    public GbgDocumentDto toGbgDocumentDto(GbgApiDocumentResponse response) {
        return GbgDocumentDto.builder()
            .id(response.getId())
            .profileId(response.getProfileId())
            .documentType(response.getDocumentType())
            .status(response.getStatus())
            .fileName(response.getFileName())
            .mimeType(response.getMimeType())
            .fileSize(response.getFileSize())
            .downloadUrl(response.getDownloadUrl())
            .uploadedAt(response.getUploadedAt())
            .expiresAt(response.getExpiresAt())
            .checksum(response.getChecksum())
            .build();
    }

    public List<GbgDocumentDto> toGbgDocumentDtoList(List<GbgApiDocumentResponse> responses) {
        return responses.stream()
            .map(this::toGbgDocumentDto)
            .collect(Collectors.toList());
    }

    public List<String> toAnalysisList(List<GbgApiAnalysisResponse> responses) {
        return responses.stream()
            .map(GbgApiAnalysisResponse::getAnalysisType)
            .collect(Collectors.toList());
    }

    public List<String> toChecklistsList(List<GbgApiChecklistResponse> responses) {
        return responses.stream()
            .map(GbgApiChecklistResponse::getChecklistName)
            .collect(Collectors.toList());
    }

    public List<String> toFormsList(List<GbgApiFormResponse> responses) {
        return responses.stream()
            .map(GbgApiFormResponse::getFormName)
            .collect(Collectors.toList());
    }
}
