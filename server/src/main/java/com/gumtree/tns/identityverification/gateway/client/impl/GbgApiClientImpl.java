package com.gumtree.tns.identityverification.gateway.client.impl;

import com.gumtree.tns.identityverification.gateway.client.GbgApiClient;
import com.gumtree.tns.identityverification.gateway.client.dto.*;
import com.gumtree.tns.identityverification.gateway.client.dto.GbgApiClientDtos.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.flogger.Flogger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.List;

/**
 * Implementation of GbgApiClient using RestTemplate
 */
@Flogger
@Component
@RequiredArgsConstructor
public class GbgApiClientImpl implements GbgApiClient {

    private final RestTemplate restTemplate;
    
    @Value("${identity-verification.gbg.base-url}")
    private String baseUrl;
    
    @Value("${identity-verification.gbg.api-key}")
    private String apiKey;

    private HttpHeaders createHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer " + apiKey);
        return headers;
    }

    @Override
    public GbgApiProfileResponse createProfile(GbgApiProfileRequest request) {
        log.atInfo().log("Creating GBG profile for customer: %s", request.getCustomerReference());
        
        HttpEntity<GbgApiProfileRequest> entity = new HttpEntity<>(request, createHeaders());
        ResponseEntity<GbgApiProfileResponse> response = restTemplate.exchange(
            baseUrl + "/profiles",
            HttpMethod.POST,
            entity,
            GbgApiProfileResponse.class
        );
        
        return response.getBody();
    }

    @Override
    public GbgApiProfileResponse getProfile(String profileId) {
        log.atInfo().log("Getting GBG profile: %s", profileId);
        
        HttpEntity<?> entity = new HttpEntity<>(createHeaders());
        ResponseEntity<GbgApiProfileResponse> response = restTemplate.exchange(
            baseUrl + "/profiles/" + profileId,
            HttpMethod.GET,
            entity,
            GbgApiProfileResponse.class
        );
        
        return response.getBody();
    }

    @Override
    public GbgApiProfileResponse updateProfile(String profileId, GbgApiProfileUpdateRequest request) {
        log.atInfo().log("Updating GBG profile: %s", profileId);
        
        HttpEntity<GbgApiProfileUpdateRequest> entity = new HttpEntity<>(request, createHeaders());
        ResponseEntity<GbgApiProfileResponse> response = restTemplate.exchange(
            baseUrl + "/profiles/" + profileId,
            HttpMethod.PUT,
            entity,
            GbgApiProfileResponse.class
        );
        
        return response.getBody();
    }

    @Override
    public GbgApiRepresentativeResponse createRepresentative(GbgApiRepresentativeRequest request) {
        log.atInfo().log("Creating representative for profile: %s", request.getProfileId());
        
        HttpEntity<GbgApiRepresentativeRequest> entity = new HttpEntity<>(request, createHeaders());
        ResponseEntity<GbgApiRepresentativeResponse> response = restTemplate.exchange(
            baseUrl + "/representatives",
            HttpMethod.POST,
            entity,
            GbgApiRepresentativeResponse.class
        );
        
        return response.getBody();
    }

    @Override
    public List<GbgApiRepresentativeResponse> getRepresentatives(String profileId) {
        log.atInfo().log("Getting representatives for profile: %s", profileId);
        
        HttpEntity<?> entity = new HttpEntity<>(createHeaders());
        ResponseEntity<List<GbgApiRepresentativeResponse>> response = restTemplate.exchange(
            baseUrl + "/profiles/" + profileId + "/representatives",
            HttpMethod.GET,
            entity,
            new ParameterizedTypeReference<List<GbgApiRepresentativeResponse>>() {}
        );
        
        return response.getBody();
    }

    @Override
    public List<GbgApiVerificationResponse> getRepresentativeVerifications(String representativeId) {
        log.atInfo().log("Getting verifications for representative: %s", representativeId);
        
        HttpEntity<?> entity = new HttpEntity<>(createHeaders());
        ResponseEntity<List<GbgApiVerificationResponse>> response = restTemplate.exchange(
            baseUrl + "/representatives/" + representativeId + "/verifications",
            HttpMethod.GET,
            entity,
            new ParameterizedTypeReference<List<GbgApiVerificationResponse>>() {}
        );
        
        return response.getBody();
    }

    @Override
    public GbgApiDocumentResponse uploadDocument(GbgApiDocumentRequest request) {
        log.atInfo().log("Uploading document for profile: %s", request.getProfileId());
        
        HttpEntity<GbgApiDocumentRequest> entity = new HttpEntity<>(request, createHeaders());
        ResponseEntity<GbgApiDocumentResponse> response = restTemplate.exchange(
            baseUrl + "/documents",
            HttpMethod.POST,
            entity,
            GbgApiDocumentResponse.class
        );
        
        return response.getBody();
    }

    @Override
    public GbgApiDocumentResponse getDocument(String documentId) {
        log.atInfo().log("Getting document: %s", documentId);
        
        HttpEntity<?> entity = new HttpEntity<>(createHeaders());
        ResponseEntity<GbgApiDocumentResponse> response = restTemplate.exchange(
            baseUrl + "/documents/" + documentId,
            HttpMethod.GET,
            entity,
            GbgApiDocumentResponse.class
        );
        
        return response.getBody();
    }

    @Override
    public List<GbgApiDocumentResponse> getProfileDocuments(String profileId) {
        log.atInfo().log("Getting documents for profile: %s", profileId);
        
        HttpEntity<?> entity = new HttpEntity<>(createHeaders());
        ResponseEntity<List<GbgApiDocumentResponse>> response = restTemplate.exchange(
            baseUrl + "/profiles/" + profileId + "/documents",
            HttpMethod.GET,
            entity,
            new ParameterizedTypeReference<List<GbgApiDocumentResponse>>() {}
        );
        
        return response.getBody();
    }

    @Override
    public GbgApiVerificationResponse submitVerification(GbgApiVerificationRequest request) {
        log.atInfo().log("Submitting verification for profile: %s", request.getProfileId());
        
        HttpEntity<GbgApiVerificationRequest> entity = new HttpEntity<>(request, createHeaders());
        ResponseEntity<GbgApiVerificationResponse> response = restTemplate.exchange(
            baseUrl + "/verifications",
            HttpMethod.POST,
            entity,
            GbgApiVerificationResponse.class
        );
        
        return response.getBody();
    }

    @Override
    public GbgApiVerificationResponse getVerificationStatus(String verificationId) {
        log.atInfo().log("Getting verification status: %s", verificationId);
        
        HttpEntity<?> entity = new HttpEntity<>(createHeaders());
        ResponseEntity<GbgApiVerificationResponse> response = restTemplate.exchange(
            baseUrl + "/verifications/" + verificationId,
            HttpMethod.GET,
            entity,
            GbgApiVerificationResponse.class
        );
        
        return response.getBody();
    }

    @Override
    public List<GbgApiProfileResponse> searchProfiles(GbgApiSearchRequest request) {
        log.atInfo().log("Searching profiles with query: %s", request.getQuery());
        
        HttpEntity<GbgApiSearchRequest> entity = new HttpEntity<>(request, createHeaders());
        ResponseEntity<List<GbgApiProfileResponse>> response = restTemplate.exchange(
            baseUrl + "/profiles/search",
            HttpMethod.POST,
            entity,
            new ParameterizedTypeReference<List<GbgApiProfileResponse>>() {}
        );
        
        return response.getBody();
    }

    @Override
    public GbgApiDomainResponse getProfileDomain(String profileId) {
        log.atInfo().log("Getting domain for profile: %s", profileId);
        
        HttpEntity<?> entity = new HttpEntity<>(createHeaders());
        ResponseEntity<GbgApiDomainResponse> response = restTemplate.exchange(
            baseUrl + "/profiles/" + profileId + "/domain",
            HttpMethod.GET,
            entity,
            GbgApiDomainResponse.class
        );
        
        return response.getBody();
    }

    @Override
    public void createProfileDomain(String profileId, GbgApiDomainRequest request) {
        log.atInfo().log("Creating domain for profile: %s", profileId);
        
        HttpEntity<GbgApiDomainRequest> entity = new HttpEntity<>(request, createHeaders());
        restTemplate.exchange(
            baseUrl + "/profiles/" + profileId + "/domain",
            HttpMethod.POST,
            entity,
            Void.class
        );
    }

    @Override
    public List<GbgApiAnalysisResponse> getProfileAnalysis(String profileId) {
        log.atInfo().log("Getting analysis for profile: %s", profileId);
        
        HttpEntity<?> entity = new HttpEntity<>(createHeaders());
        ResponseEntity<List<GbgApiAnalysisResponse>> response = restTemplate.exchange(
            baseUrl + "/profiles/" + profileId + "/analysis",
            HttpMethod.GET,
            entity,
            new ParameterizedTypeReference<List<GbgApiAnalysisResponse>>() {}
        );
        
        return response.getBody();
    }

    @Override
    public List<GbgApiChecklistResponse> getProfileChecklists(String profileId) {
        log.atInfo().log("Getting checklists for profile: %s", profileId);
        
        HttpEntity<?> entity = new HttpEntity<>(createHeaders());
        ResponseEntity<List<GbgApiChecklistResponse>> response = restTemplate.exchange(
            baseUrl + "/profiles/" + profileId + "/checklists",
            HttpMethod.GET,
            entity,
            new ParameterizedTypeReference<List<GbgApiChecklistResponse>>() {}
        );
        
        return response.getBody();
    }

    @Override
    public List<GbgApiFormResponse> getProfileForms(String profileId) {
        log.atInfo().log("Getting forms for profile: %s", profileId);
        
        HttpEntity<?> entity = new HttpEntity<>(createHeaders());
        ResponseEntity<List<GbgApiFormResponse>> response = restTemplate.exchange(
            baseUrl + "/profiles/" + profileId + "/forms",
            HttpMethod.GET,
            entity,
            new ParameterizedTypeReference<List<GbgApiFormResponse>>() {}
        );
        
        return response.getBody();
    }
}