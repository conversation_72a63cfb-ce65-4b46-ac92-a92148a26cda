package com.gumtree.tns.identityverification.dto;

import com.gumtree.tns.identityverification.model.VerificationType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * DTO for initiating verification request
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InitiateVerificationRequestDto {

    @NotNull
    private VerificationType verificationType;

    private String customerReference;

    private PersonalInfoDto personalInfo;

    private BusinessInfoDto businessInfo;
}
