package com.gumtree.tns.identityverification.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * DTO for Subject Access Response
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SarResponseDto {

    private String requestId;

    private String subtaskId;

    private boolean error;

    private String reason;

    private String salutation;

    private List<PersonalDataSegmentDto> personalDataSegments;
}
