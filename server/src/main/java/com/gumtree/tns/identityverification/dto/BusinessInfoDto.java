package com.gumtree.tns.identityverification.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * DTO for business information in KYB verification
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BusinessInfoDto {

    @NotBlank
    private String companyName;

    @NotBlank
    private String companyNumber;

    @NotNull
    private AddressDto address;

    private String domain;

    private String phoneNumber;

    private String email;

    private String website;

    private String businessType;

    private String industryCode;

    private String vatNumber;

    private String taxId;

    private List<RepresentativeInfoDto> representatives;

    private String incorporationDate;

    private String incorporationCountry;

    private String legalForm;

    private String description;
}
