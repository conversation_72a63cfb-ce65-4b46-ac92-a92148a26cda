package com.gumtree.tns.identityverification.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * DTO for document upload response
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocumentResponseDto {

    private UUID documentId;

    private String documentType;

    private String status;

    private String fileName;

    private String mimeType;

    private Long fileSize;

    private LocalDateTime uploadedAt;

    private String gbgDocumentId;

    private String downloadUrl;

    private LocalDateTime expiresAt;
}
