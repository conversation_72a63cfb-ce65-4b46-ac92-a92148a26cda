package com.gumtree.tns.identityverification.service.mapper;

import com.gumtree.tns.identityverification.dto.*;
import com.gumtree.tns.identityverification.model.*;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper for converting between API models and DTOs
 */
@Component
public class VerificationControllerMapper {

    public InitiateVerificationRequestDto toInitiateVerificationRequestDto(InitiateVerificationRequest request) {
        return InitiateVerificationRequestDto.builder()
            .verificationType(mapVerificationType(request.getVerificationType()))
            .customerReference(request.getCustomerReference())
            .personalInfo(mapPersonalInfo(request.getPersonalInfo()))
            .build();
    }

    public InitiateVerificationRequestDto toInitiateVerificationRequestDto(InitiateBusinessVerificationRequest request) {
        return InitiateVerificationRequestDto.builder()
            .verificationType(mapVerificationType(request.getVerificationType()))
            .customerReference(request.getCustomerReference())
            .businessInfo(mapBusinessInfo(request.getBusinessInfo()))
            .build();
    }

    public VerificationResponse toVerificationResponse(VerificationResponseDto dto) {
        return new VerificationResponse()
            .verificationId(dto.getVerificationId())
            .status(mapVerificationStatus(dto.getStatus()))
            .gbgProfileId(dto.getGbgProfileId())
            .createdAt(toOffsetDateTime(dto.getCreatedAt()))
            .estimatedCompletionTime(toOffsetDateTime(dto.getEstimatedCompletionTime()));
    }

    public VerificationStatusResponse toVerificationStatusResponse(VerificationStatusResponseDto dto) {
        return new VerificationStatusResponse()
            .verificationId(dto.getVerificationId())
            .userId(dto.getUserId())
            .accountId(dto.getAccountId())
            .status(mapVerificationStatus(dto.getStatus()))
            .riskScore(dto.getRiskScore())
            .riskLevel(mapRiskLevel(dto.getRiskLevel()))
            .completedSteps(mapVerificationSteps(dto.getCompletedSteps()))
            .nextSteps(mapVerificationSteps(dto.getNextSteps()))
            .createdAt(toOffsetDateTime(dto.getCreatedAt()))
            .updatedAt(toOffsetDateTime(dto.getUpdatedAt()))
            .completedAt(toOffsetDateTime(dto.getCompletedAt()));
    }

    public DocumentUploadDto toDocumentUploadDto(DocumentType documentType, MultipartFile file, String description) {
        return DocumentUploadDto.builder()
            .documentType(documentType.name())
            .fileContent(convertToByteArray(file))
            .fileName(file.getOriginalFilename())
            .mimeType(file.getContentType())
            .description(description)
            .build();
    }

    private byte[] convertToByteArray(MultipartFile file) {
        try {
            return file.getBytes();
        } catch (IOException e) {
            throw new RuntimeException("Failed to convert MultipartFile to byte array", e);
        }
    }

    public DocumentResponse toDocumentResponse(DocumentResponseDto dto) {
        return new DocumentResponse()
            .documentId(dto.getDocumentId())
            .documentType(DocumentType.valueOf(dto.getDocumentType()))
            .status(DocumentStatus.valueOf(dto.getStatus()))
            .uploadedAt(toOffsetDateTime(dto.getUploadedAt()));
    }

    private OffsetDateTime toOffsetDateTime(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }
        return localDateTime.atOffset(ZoneOffset.UTC);
    }

    public VerificationRequestsPageResponse toVerificationRequestsPageResponse(Page<VerificationStatusResponseDto> page) {
        return new VerificationRequestsPageResponse()
            .content(page.getContent().stream()
                .map(this::toVerificationStatusResponse)
                .collect(Collectors.toList()))
            .totalElements(page.getTotalElements())
            .totalPages(page.getTotalPages())
            .size(page.getSize())
            .number(page.getNumber());
    }

    // Helper mapping methods
    private VerificationType mapVerificationType(VerificationType type) {
        return type;
    }

    private VerificationStatus mapVerificationStatus(com.gumtree.tns.identityverification.dao.model.VerificationStatus status) {
        return VerificationStatus.valueOf(status.name());
    }

    private RiskLevel mapRiskLevel(com.gumtree.tns.identityverification.dao.model.RiskLevel riskLevel) {
        if (riskLevel == null) return null;
        return RiskLevel.valueOf(riskLevel.name());
    }

    private PersonalInfoDto mapPersonalInfo(PersonalInfo personalInfo) {
        if (personalInfo == null) return null;
        
        return PersonalInfoDto.builder()
            .firstName(personalInfo.getFirstName())
            .lastName(personalInfo.getLastName())
            .middleName(personalInfo.getMiddleName())
            .dateOfBirth(personalInfo.getDateOfBirth())
            .nationality(personalInfo.getNationality())
            .address(mapAddress(personalInfo.getAddress()))
            .build();
    }

    private BusinessInfoDto mapBusinessInfo(BusinessInfo businessInfo) {
        if (businessInfo == null) return null;
        
        return BusinessInfoDto.builder()
            .companyName(businessInfo.getCompanyName())
            .companyNumber(businessInfo.getCompanyNumber())
            .address(mapAddress(businessInfo.getAddress()))
            .domain(businessInfo.getDomain())
            .representatives(mapRepresentatives(businessInfo.getRepresentatives()))
            .build();
    }

    private AddressDto mapAddress(Address address) {
        if (address == null) return null;
        
        return AddressDto.builder()
            .street(address.getStreet())
            .city(address.getCity())
            .state(address.getState())
            .postalCode(address.getPostalCode())
            .countryCode(address.getCountryCode())
            .build();
    }

    private List<RepresentativeInfoDto> mapRepresentatives(List<RepresentativeInfo> representatives) {
        if (representatives == null) return null;
        
        return representatives.stream()
            .map(this::mapRepresentative)
            .collect(Collectors.toList());
    }

    private RepresentativeInfoDto mapRepresentative(RepresentativeInfo representative) {
        return RepresentativeInfoDto.builder()
            .type(com.gumtree.tns.identityverification.dao.model.RepresentativeType.valueOf(representative.getType().name()))
            .personalInfo(mapPersonalInfo(representative.getPersonalInfo()))
            .build();
    }

    private List<VerificationStep> mapVerificationSteps(List<VerificationStepDto> steps) {
        if (steps == null) return null;
        
        return steps.stream()
            .map(this::mapVerificationStep)
            .collect(Collectors.toList());
    }

    private VerificationStep mapVerificationStep(VerificationStepDto step) {
        return new VerificationStep()
            .stepType(VerificationStep.StepTypeEnum.fromValue(step.getStepType()))
            .status(VerificationStep.StatusEnum.valueOf(step.getStatus()))
            .completedAt(toOffsetDateTime(step.getCompletedAt()));
    }

    private String extractFileName(DocumentUploadRequest request) {
        // Extract filename from multipart request
        return "document_" + System.currentTimeMillis();
    }

    private String detectMimeType(DocumentUploadRequest request) {
        // Detect MIME type from file content
        return "application/octet-stream";
    }
}
