package com.gumtree.tns.identityverification.service;

import com.gumtree.tns.identityverification.dto.DocumentUploadDto;
import com.gumtree.tns.identityverification.dto.DocumentResponseDto;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Service interface for document operations
 */
public interface DocumentService {

    /**
     * Upload document for verification request
     *
     * @param verificationId the verification request ID
     * @param documentDto the document upload details
     * @return document response
     */
    DocumentResponseDto uploadDocument(UUID verificationId, DocumentUploadDto documentDto);

    /**
     * Get document by ID
     *
     * @param documentId the document ID
     * @return document response
     */
    Optional<DocumentResponseDto> getDocument(UUID documentId);

    /**
     * Get documents for verification request
     *
     * @param verificationId the verification request ID
     * @return list of documents
     */
    List<DocumentResponseDto> getDocumentsForVerification(UUID verificationId);

    /**
     * Delete document
     *
     * @param documentId the document ID
     * @return true if deleted successfully
     */
    boolean deleteDocument(UUID documentId);

    /**
     * Update document status
     *
     * @param documentId the document ID
     * @param status the new status
     * @return updated document
     */
    DocumentResponseDto updateDocumentStatus(UUID documentId, String status);

    /**
     * Validate document
     *
     * @param documentDto the document to validate
     * @return validation result
     */
    boolean validateDocument(DocumentUploadDto documentDto);

    /**
     * Get document download URL
     *
     * @param documentId the document ID
     * @return download URL
     */
    Optional<String> getDocumentDownloadUrl(UUID documentId);
}
