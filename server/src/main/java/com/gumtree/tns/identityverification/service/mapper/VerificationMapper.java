package com.gumtree.tns.identityverification.service.mapper;

import com.gumtree.tns.identityverification.dao.model.IdentityVerificationRequest;
import com.gumtree.tns.identityverification.dao.model.VerificationStep;
import com.gumtree.tns.identityverification.dao.model.VerificationDocument;
import com.gumtree.tns.identityverification.dto.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.flogger.Flogger;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper for verification-related entities and DTOs
 */
@Flogger
@Component
@RequiredArgsConstructor
public class VerificationMapper {

    private final ObjectMapper objectMapper;

    public VerificationResponseDto toVerificationResponseDto(IdentityVerificationRequest request) {
        return VerificationResponseDto.builder()
            .verificationId(request.getId())
            .status(request.getStatus())
            .gbgProfileId(request.getGbgProfileId())
            .createdAt(request.getCreatedAt())
            .estimatedCompletionTime(request.getCreatedAt().plusDays(7)) // Default 7 days
            .build();
    }

    public VerificationStatusResponseDto toVerificationStatusResponseDto(IdentityVerificationRequest request) {
        return VerificationStatusResponseDto.builder()
            .verificationId(request.getId())
            .userId(request.getUserId())
            .accountId(request.getAccountId())
            .status(request.getStatus())
            .riskScore(request.getRiskScore())
            .riskLevel(request.getRiskLevel())
            .completedSteps(mapVerificationSteps(request.getVerificationSteps(), true))
            .nextSteps(mapVerificationSteps(request.getVerificationSteps(), false))
            .createdAt(request.getCreatedAt())
            .updatedAt(request.getUpdatedAt())
            .completedAt(request.getCompletedAt())
            .build();
    }

    public DocumentResponseDto toDocumentResponseDto(VerificationDocument document) {
        return DocumentResponseDto.builder()
            .documentId(document.getId())
            .documentType(document.getDocumentType().name())
            .status(document.getDocumentStatus().name())
            .fileName(document.getFileName())
            .mimeType(document.getMimeType())
            .fileSize(document.getFileSize())
            .uploadedAt(document.getCreatedAt())
            .gbgDocumentId(document.getGbgDocumentId())
            .downloadUrl(document.getDocumentUrl())
            .build();
    }

    public String toJsonString(Object object) {
        try {
            return objectMapper.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            log.atWarning().withCause(e).log("Failed to serialize object to JSON: %s", object.getClass().getSimpleName());
            return "{}";
        }
    }

    public <T> T fromJsonString(String json, Class<T> clazz) {
        try {
            return objectMapper.readValue(json, clazz);
        } catch (JsonProcessingException e) {
            log.atWarning().withCause(e).log("Failed to deserialize JSON to %s: %s", clazz.getSimpleName(), json);
            return null;
        }
    }

    public PersonalInfoDto fromPersonalInfoJson(String json) {
        return fromJsonString(json, PersonalInfoDto.class);
    }

    public BusinessInfoDto fromBusinessInfoJson(String json) {
        return fromJsonString(json, BusinessInfoDto.class);
    }

    private List<VerificationStepDto> mapVerificationSteps(List<VerificationStep> steps, boolean completed) {
        if (steps == null) {
            return List.of();
        }

        return steps.stream()
            .filter(step -> completed ? 
                VerificationStep.StepStatus.COMPLETED.equals(step.getStepStatus()) :
                !VerificationStep.StepStatus.COMPLETED.equals(step.getStepStatus()))
            .map(this::toVerificationStepDto)
            .collect(Collectors.toList());
    }

    private VerificationStepDto toVerificationStepDto(VerificationStep step) {
        return VerificationStepDto.builder()
            .stepType(step.getStepType().name())
            .status(step.getStepStatus().name())
            .completedAt(step.getCompletedAt())
            .gbgVerificationId(step.getGbgVerificationId())
            .providerData(fromJsonString(step.getProviderData(), Object.class))
            .build();
    }
}
