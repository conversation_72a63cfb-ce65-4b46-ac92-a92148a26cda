package com.gumtree.tns.identityverification.exception;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Standard API error response structure
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApiErrorResponse {

    /**
     * Timestamp when the error occurred
     */
    private LocalDateTime timestamp;

    /**
     * HTTP status code
     */
    private int status;

    /**
     * Error type/category
     */
    private String error;

    /**
     * Error message
     */
    private String message;

    /**
     * Request path where error occurred
     */
    private String path;

    /**
     * Validation errors (field -> error message)
     */
    private Map<String, String> validationErrors;

    /**
     * Additional error details
     */
    private Map<String, Object> details;

    /**
     * Trace ID for debugging
     */
    private String traceId;
}
