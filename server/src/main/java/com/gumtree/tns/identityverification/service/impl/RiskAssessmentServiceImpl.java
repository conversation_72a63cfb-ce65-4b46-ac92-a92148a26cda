package com.gumtree.tns.identityverification.service.impl;

import com.gumtree.tns.identityverification.dao.model.IdentityVerificationRequest;
import com.gumtree.tns.identityverification.dao.model.RiskAssessment;
import com.gumtree.tns.identityverification.dao.model.RiskLevel;
import com.gumtree.tns.identityverification.dao.model.VerificationDocument;
import com.gumtree.tns.identityverification.dao.repository.RiskAssessmentRepository;
import com.gumtree.tns.identityverification.service.RiskAssessmentService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.flogger.Flogger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

/**
 * Implementation of RiskAssessmentService
 */
@Flogger
@Service
@RequiredArgsConstructor
public class RiskAssessmentServiceImpl implements RiskAssessmentService {

    private final RiskAssessmentRepository riskAssessmentRepository;
    private final ObjectMapper objectMapper;

    // Risk scoring thresholds
    private static final int LOW_RISK_THRESHOLD = 30;
    private static final int MEDIUM_RISK_THRESHOLD = 60;
    private static final int HIGH_RISK_THRESHOLD = 80;

    @Override
    @Transactional(readOnly = true)
    public Integer calculateRiskScore(IdentityVerificationRequest verificationRequest) {
        log.atInfo().log("Calculating risk score for verification: %s", verificationRequest.getId());
        
        Map<String, Object> riskFactors = new HashMap<>();
        int totalScore = 0;
        
        // Base score
        int baseScore = 10;
        totalScore += baseScore;
        riskFactors.put("baseScore", baseScore);
        
        // Verification type factor
        int verificationTypeScore = calculateVerificationTypeScore(verificationRequest);
        totalScore += verificationTypeScore;
        riskFactors.put("verificationTypeScore", verificationTypeScore);
        
        // Document completeness factor
        int documentScore = calculateDocumentScore(verificationRequest);
        totalScore += documentScore;
        riskFactors.put("documentScore", documentScore);
        
        // Time factor (how long verification is taking)
        int timeScore = calculateTimeScore(verificationRequest);
        totalScore += timeScore;
        riskFactors.put("timeScore", timeScore);
        
        // Representative factor (for KYB)
        int representativeScore = calculateRepresentativeScore(verificationRequest);
        totalScore += representativeScore;
        riskFactors.put("representativeScore", representativeScore);
        
        // GBG data factor
        int gbgScore = calculateGbgScore(verificationRequest);
        totalScore += gbgScore;
        riskFactors.put("gbgScore", gbgScore);
        
        // Ensure score is within bounds
        totalScore = Math.max(0, Math.min(100, totalScore));
        
        log.atInfo().log("Calculated risk score %d for verification %s", totalScore, verificationRequest.getId());
        
        return totalScore;
    }

    @Override
    @Transactional
    public RiskAssessment calculateAndSaveRiskAssessment(IdentityVerificationRequest verificationRequest) {
        log.atInfo().log("Calculating and saving risk assessment for verification: %s", verificationRequest.getId());
        
        Integer riskScore = calculateRiskScore(verificationRequest);
        RiskLevel riskLevel = determineRiskLevel(riskScore);
        
        Map<String, Object> riskFactors = new HashMap<>();
        riskFactors.put("calculatedAt", LocalDateTime.now());
        riskFactors.put("verificationType", verificationRequest.getVerificationType());
        riskFactors.put("documentCount", verificationRequest.getVerificationDocuments() != null ? 
            verificationRequest.getVerificationDocuments().size() : 0);
        riskFactors.put("representativeCount", verificationRequest.getRepresentatives() != null ? 
            verificationRequest.getRepresentatives().size() : 0);
        
        Map<String, Object> assessmentDetails = new HashMap<>();
        assessmentDetails.put("algorithm", "STANDARD_V1");
        assessmentDetails.put("factors", riskFactors);
        assessmentDetails.put("thresholds", Map.of(
            "low", LOW_RISK_THRESHOLD,
            "medium", MEDIUM_RISK_THRESHOLD,
            "high", HIGH_RISK_THRESHOLD
        ));
        
        try {
            RiskAssessment riskAssessment = RiskAssessment.builder()
                .verificationRequest(verificationRequest)
                .riskFactors(objectMapper.writeValueAsString(riskFactors))
                .riskScore(riskScore)
                .riskLevel(riskLevel)
                .assessmentDetails(objectMapper.writeValueAsString(assessmentDetails))
                .assessmentDate(LocalDateTime.now())
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();
            
            riskAssessment = riskAssessmentRepository.save(riskAssessment);
            
            // Update verification request with latest risk score
            verificationRequest.setRiskScore(riskScore);
            verificationRequest.setRiskLevel(riskLevel);
            
            log.atInfo().log("Risk assessment saved with score %d and level %s for verification %s", 
                riskScore, riskLevel, verificationRequest.getId());
            
            return riskAssessment;
            
        } catch (Exception e) {
            log.atSevere().withCause(e).log("Failed to save risk assessment for verification: %s", 
                verificationRequest.getId());
            throw new RuntimeException("Failed to save risk assessment", e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<RiskAssessment> getRiskAssessment(UUID verificationId) {
        return riskAssessmentRepository.findTopByVerificationRequestIdOrderByAssessmentDateDesc(verificationId);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<RiskAssessment> getLatestRiskAssessment(UUID verificationId) {
        return riskAssessmentRepository.findTopByVerificationRequestIdOrderByAssessmentDateDesc(verificationId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<RiskAssessment> getAllRiskAssessments(UUID verificationId) {
        return riskAssessmentRepository.findByVerificationRequestId(verificationId);
    }

    @Override
    public RiskLevel determineRiskLevel(Integer riskScore) {
        if (riskScore == null) {
            return RiskLevel.MEDIUM;
        }
        
        if (riskScore <= LOW_RISK_THRESHOLD) {
            return RiskLevel.LOW;
        } else if (riskScore <= MEDIUM_RISK_THRESHOLD) {
            return RiskLevel.MEDIUM;
        } else if (riskScore <= HIGH_RISK_THRESHOLD) {
            return RiskLevel.HIGH;
        } else {
            return RiskLevel.CRITICAL;
        }
    }

    @Override
    @Transactional
    public RiskAssessment updateRiskAssessment(UUID riskAssessmentId, Integer riskScore, String riskFactors) {
        RiskAssessment riskAssessment = riskAssessmentRepository.findById(riskAssessmentId)
            .orElseThrow(() -> new IllegalArgumentException("Risk assessment not found: " + riskAssessmentId));
        
        riskAssessment.setRiskScore(riskScore);
        riskAssessment.setRiskLevel(determineRiskLevel(riskScore));
        riskAssessment.setRiskFactors(riskFactors);
        riskAssessment.setUpdatedAt(LocalDateTime.now());
        
        return riskAssessmentRepository.save(riskAssessment);
    }

    @Override
    @Transactional(readOnly = true)
    public List<RiskAssessment> getRiskAssessmentsByLevel(RiskLevel riskLevel) {
        return riskAssessmentRepository.findByRiskLevel(riskLevel);
    }

    @Override
    @Transactional(readOnly = true)
    public Double getAverageRiskScore(int days) {
        LocalDateTime startDate = LocalDateTime.now().minusDays(days);
        LocalDateTime endDate = LocalDateTime.now();
        
        return riskAssessmentRepository.getAverageRiskScore(startDate, endDate);
    }

    // Helper methods for risk calculation
    private int calculateVerificationTypeScore(IdentityVerificationRequest request) {
        // KYB typically has higher base risk than KYC
        return request.getVerificationType().name().equals("KYB") ? 5 : 0;
    }

    private int calculateDocumentScore(IdentityVerificationRequest request) {
        if (request.getVerificationDocuments() == null || request.getVerificationDocuments().isEmpty()) {
            return 20; // High risk if no documents
        }
        
        long verifiedDocuments = request.getVerificationDocuments().stream()
            .filter(doc -> VerificationDocument.DocumentStatus.VERIFIED.equals(doc.getDocumentStatus()))
            .count();
        
        if (verifiedDocuments == 0) {
            return 15; // Medium-high risk if no verified documents
        } else if (verifiedDocuments < request.getVerificationDocuments().size()) {
            return 5; // Low risk if some documents verified
        } else {
            return 0; // No additional risk if all documents verified
        }
    }

    private int calculateTimeScore(IdentityVerificationRequest request) {
        LocalDateTime now = LocalDateTime.now();
        long daysElapsed = java.time.Duration.between(request.getCreatedAt(), now).toDays();
        
        if (daysElapsed > 30) {
            return 15; // High risk for very old verifications
        } else if (daysElapsed > 14) {
            return 10; // Medium risk for old verifications
        } else if (daysElapsed > 7) {
            return 5; // Low risk for moderately old verifications
        } else {
            return 0; // No additional risk for recent verifications
        }
    }

    private int calculateRepresentativeScore(IdentityVerificationRequest request) {
        if (request.getRepresentatives() == null || request.getRepresentatives().isEmpty()) {
            // For KYB, missing representatives is high risk
            return request.getVerificationType().name().equals("KYB") ? 15 : 0;
        }
        
        long verifiedRepresentatives = request.getRepresentatives().stream()
            .filter(rep -> com.gumtree.tns.identityverification.dao.model.Representative.VerificationStatus.VERIFIED
                .equals(rep.getVerificationStatus()))
            .count();
        
        if (verifiedRepresentatives == 0) {
            return 10; // Medium risk if no verified representatives
        } else {
            return 0; // No additional risk if representatives verified
        }
    }

    private int calculateGbgScore(IdentityVerificationRequest request) {
        if (request.getGbgProfileId() == null) {
            return 10; // Medium risk if no GBG integration
        }
        
        // This would typically integrate with GBG risk scores
        // For now, return a default low risk
        return 0;
    }
}
