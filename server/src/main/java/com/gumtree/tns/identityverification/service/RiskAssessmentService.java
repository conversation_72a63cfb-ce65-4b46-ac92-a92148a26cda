package com.gumtree.tns.identityverification.service;

import com.gumtree.tns.identityverification.dao.model.IdentityVerificationRequest;
import com.gumtree.tns.identityverification.dao.model.RiskAssessment;
import com.gumtree.tns.identityverification.dao.model.RiskLevel;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Service interface for risk assessment operations
 */
public interface RiskAssessmentService {

    /**
     * Calculate risk score for verification request
     *
     * @param verificationRequest the verification request
     * @return calculated risk score
     */
    Integer calculateRiskScore(IdentityVerificationRequest verificationRequest);

    /**
     * Calculate and save risk assessment
     *
     * @param verificationRequest the verification request
     * @return saved risk assessment
     */
    RiskAssessment calculateAndSaveRiskAssessment(IdentityVerificationRequest verificationRequest);

    /**
     * Get risk assessment for verification request
     *
     * @param verificationId the verification request ID
     * @return risk assessment if exists
     */
    Optional<RiskAssessment> getRiskAssessment(UUID verificationId);

    /**
     * Get latest risk assessment for verification request
     *
     * @param verificationId the verification request ID
     * @return latest risk assessment if exists
     */
    Optional<RiskAssessment> getLatestRiskAssessment(UUID verificationId);

    /**
     * Get all risk assessments for verification request
     *
     * @param verificationId the verification request ID
     * @return list of risk assessments
     */
    List<RiskAssessment> getAllRiskAssessments(UUID verificationId);

    /**
     * Determine risk level from risk score
     *
     * @param riskScore the risk score
     * @return risk level
     */
    RiskLevel determineRiskLevel(Integer riskScore);

    /**
     * Update risk assessment
     *
     * @param riskAssessmentId the risk assessment ID
     * @param riskScore the new risk score
     * @param riskFactors the risk factors
     * @return updated risk assessment
     */
    RiskAssessment updateRiskAssessment(UUID riskAssessmentId, Integer riskScore, String riskFactors);

    /**
     * Get risk assessments by risk level
     *
     * @param riskLevel the risk level
     * @return list of risk assessments
     */
    List<RiskAssessment> getRiskAssessmentsByLevel(RiskLevel riskLevel);

    /**
     * Get average risk score for a time period
     *
     * @param days the number of days to look back
     * @return average risk score
     */
    Double getAverageRiskScore(int days);
}
