<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.gumtree.tns</groupId>
        <artifactId>identity-verification-service</artifactId>
        <version>3.0-SNAPSHOT</version>
    </parent>

    <groupId>com.gumtree.tns.identity-verification-service</groupId>
    <artifactId>contract</artifactId>
    <name>Identity Verification Service Contract</name>
    <description>Identity Verification Service Contract</description>

    <build>
        <plugins>
            <plugin>
                <groupId>org.openapitools.openapistylevalidator</groupId>
                <artifactId>openapi-style-validator-maven-plugin</artifactId>
                <version>1.8</version>
                <executions>
                    <execution>
                        <phase>verify</phase>
                        <goals>
                            <goal>validate</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <inputFile>${project.basedir}/contract.yaml</inputFile>
                    <validateInfoLicense>false</validateInfoLicense>
                    <validateInfoDescription>false</validateInfoDescription>
                    <validateInfoContact>false</validateInfoContact>
                    <validateOperationOperationId>true</validateOperationOperationId>
                    <validateOperationDescription>false</validateOperationDescription>
                    <validateOperationTag>false</validateOperationTag>
                    <validateOperationSummary>false</validateOperationSummary>
                    <validateModelPropertiesExample>false</validateModelPropertiesExample>
                    <validateModelPropertiesDescription>false</validateModelPropertiesDescription>
                    <validateModelRequiredProperties>true</validateModelRequiredProperties>
                    <validateModelNoLocalDef>true</validateModelNoLocalDef>
                    <validateNaming>true</validateNaming>
                    <ignoreHeaderXNaming>true</ignoreHeaderXNaming>
                    <headerNamingConvention>HyphenCase</headerNamingConvention>
                    <pathNamingConvention>HyphenCase</pathNamingConvention>
                    <parameterNamingConvention>HyphenCase</parameterNamingConvention>
                    <headerNamingConvention>HyphenCase</headerNamingConvention>
                    <propertyNamingConvention>CamelCase</propertyNamingConvention>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
