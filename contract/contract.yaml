openapi: 3.0.0
info:
  version: 3.0-SNAPSHOT
  title: identity-verification-service
  description: Identity Verification Service for KYC/KYB authentication using GBG integration
tags:
  - name: identityVerification
    description: Identity Verification API for users and accounts
  - name: verificationStatus
    description: Verification status query API
  - name: gdpr
    description: GDPR compliance API for data access and deletion
  - name: admin
    description: Administrative operations for customer service
paths:
  '/users/{user-id}/identity-verification':
    post:
      tags:
        - identityVerification
      operationId: initiateUserIdentityVerification
      summary: Initiate identity verification for a user
      description: Start KYC process for an individual user
      security:
        - bearerAuth: []
      parameters:
        - name: user-id
          in: path
          description: User ID
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InitiateVerificationRequest'
      responses:
        '201':
          description: Verification initiated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VerificationResponse'
        default:
          $ref: '#/components/responses/ApiErrorResponse'
    get:
      tags:
        - verificationStatus
      operationId: getUserVerificationStatus
      summary: Get user verification status
      description: Retrieve current verification status for a user
      parameters:
        - name: user-id
          in: path
          description: User ID
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Verification status retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VerificationStatusResponse'
        default:
          $ref: '#/components/responses/ApiErrorResponse'
  '/accounts/{account-id}/identity-verification':
    post:
      tags:
        - identityVerification
      operationId: initiateAccountIdentityVerification
      summary: Initiate identity verification for an account
      description: Start KYB process for a business account
      security:
        - bearerAuth: []
      parameters:
        - name: account-id
          in: path
          description: Account ID
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InitiateBusinessVerificationRequest'
      responses:
        '201':
          description: Business verification initiated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VerificationResponse'
        default:
          $ref: '#/components/responses/ApiErrorResponse'
    get:
      tags:
        - verificationStatus
      operationId: getAccountVerificationStatus
      summary: Get account verification status
      description: Retrieve current verification status for an account
      parameters:
        - name: account-id
          in: path
          description: Account ID
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Account verification status retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VerificationStatusResponse'
        default:
          $ref: '#/components/responses/ApiErrorResponse'
  '/verification-requests/{verification-id}/documents':
    post:
      tags:
        - identityVerification
      operationId: uploadVerificationDocument
      summary: Upload verification document
      description: Upload document for identity verification process
      security:
        - bearerAuth: []
      parameters:
        - name: verification-id
          in: path
          description: Verification request ID
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/DocumentUploadRequest'
      responses:
        '201':
          description: Document uploaded successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentResponse'
        default:
          $ref: '#/components/responses/ApiErrorResponse'
  '/verification-requests/{verification-id}/submit':
    post:
      tags:
        - identityVerification
      operationId: submitVerificationRequest
      summary: Submit verification request for processing
      description: Submit completed verification request to GBG for processing
      security:
        - bearerAuth: []
      parameters:
        - name: verification-id
          in: path
          description: Verification request ID
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Verification request submitted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VerificationResponse'
        default:
          $ref: '#/components/responses/ApiErrorResponse'
  '/gdpr/sar':
    put:
      tags:
        - gdpr
      summary: Subject Access Request
      operationId: getUserData
      description: Retrieve user data for GDPR compliance
      security:
        - apiKeyAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SarRequest'
      responses:
        '200':
          description: User data retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SarResponse'
        default:
          $ref: '#/components/responses/ApiErrorResponse'
  '/gdpr/ddr':
    put:
      tags:
        - gdpr
      summary: Data Deletion Request
      operationId: deleteUserData
      description: Delete user data for GDPR compliance
      security:
        - apiKeyAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DdrRequest'
      responses:
        '200':
          description: User data deleted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DdrResponse'
        default:
          $ref: '#/components/responses/ApiErrorResponse'
  '/admin/verification-requests':
    get:
      tags:
        - admin
      operationId: getVerificationRequests
      summary: Get verification requests for admin review
      description: Retrieve verification requests for customer service review
      security:
        - csApiKeyAuth: []
      parameters:
        - name: status
          in: query
          description: Filter by verification status
          schema:
            $ref: '#/components/schemas/VerificationStatus'
        - name: user-id
          in: query
          description: Filter by user ID
          schema:
            type: integer
            format: int64
        - name: account-id
          in: query
          description: Filter by account ID
          schema:
            type: integer
            format: int64
        - name: page
          in: query
          description: Page number for pagination
          schema:
            type: integer
            default: 0
        - name: size
          in: query
          description: Page size for pagination
          schema:
            type: integer
            default: 20
      responses:
        '200':
          description: Verification requests retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VerificationRequestsPageResponse'
        default:
          $ref: '#/components/responses/ApiErrorResponse'
components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
    apiKeyAuth:
      type: apiKey
      in: header
      name: X-Service-Key
    csApiKeyAuth:
      type: apiKey
      in: header
      name: X-CS-Api-Key
  responses:
    ApiErrorResponse:
      description: Generic API error response
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ApiError'
  schemas:
    InitiateVerificationRequest:
      type: object
      description: Request to initiate user identity verification
      required:
        - verificationType
        - personalInfo
      properties:
        verificationType:
          $ref: '#/components/schemas/VerificationType'
        personalInfo:
          $ref: '#/components/schemas/PersonalInfo'
        customerReference:
          type: string
          description: Customer reference for tracking
          example: "user_12345_kyc"
    InitiateBusinessVerificationRequest:
      type: object
      description: Request to initiate business identity verification
      required:
        - verificationType
        - businessInfo
      properties:
        verificationType:
          $ref: '#/components/schemas/VerificationType'
        businessInfo:
          $ref: '#/components/schemas/BusinessInfo'
        customerReference:
          type: string
          description: Customer reference for tracking
          example: "account_67890_kyb"
    VerificationResponse:
      type: object
      description: Response for verification request
      properties:
        verificationId:
          type: string
          format: uuid
          description: Unique verification request ID
          example: "550e8400-e29b-41d4-a716-************"
        status:
          $ref: '#/components/schemas/VerificationStatus'
        gbgProfileId:
          type: string
          description: GBG profile ID
          example: "GBG-12345"
        createdAt:
          type: string
          format: date-time
          description: Creation timestamp
          example: "2023-01-01T10:00:00Z"
        estimatedCompletionTime:
          type: string
          format: date-time
          description: Estimated completion time
          example: "2023-01-01T12:00:00Z"
    VerificationStatusResponse:
      type: object
      description: Verification status response
      properties:
        verificationId:
          type: string
          format: uuid
          description: Verification request ID
          example: "550e8400-e29b-41d4-a716-************"
        userId:
          type: integer
          format: int64
          description: User ID (for KYC)
          example: 12345
        accountId:
          type: integer
          format: int64
          description: Account ID (for KYB)
          example: 67890
        status:
          $ref: '#/components/schemas/VerificationStatus'
        riskScore:
          type: integer
          minimum: 0
          maximum: 100
          description: Risk score (0-100)
          example: 75
        riskLevel:
          $ref: '#/components/schemas/RiskLevel'
        completedSteps:
          type: array
          items:
            $ref: '#/components/schemas/VerificationStep'
        nextSteps:
          type: array
          items:
            $ref: '#/components/schemas/VerificationStep'
        createdAt:
          type: string
          format: date-time
          description: Creation timestamp
          example: "2023-01-01T10:00:00Z"
        updatedAt:
          type: string
          format: date-time
          description: Last update timestamp
          example: "2023-01-01T11:00:00Z"
        completedAt:
          type: string
          format: date-time
          description: Completion timestamp
          example: "2023-01-01T12:00:00Z"
    DocumentUploadRequest:
      type: object
      description: Document upload request
      required:
        - documentType
        - file
      properties:
        documentType:
          $ref: '#/components/schemas/DocumentType'
        file:
          type: string
          format: binary
          description: Document file
          example: !!binary |-
            Y2FjY2E=
        description:
          type: string
          description: Document description
          example: "Passport copy for verification"
    DocumentResponse:
      type: object
      description: Document upload response
      properties:
        documentId:
          type: string
          format: uuid
          description: Document ID
          example: "550e8400-e29b-41d4-a716-************"
        documentType:
          $ref: '#/components/schemas/DocumentType'
        status:
          $ref: '#/components/schemas/DocumentStatus'
        uploadedAt:
          type: string
          format: date-time
          description: Upload timestamp
          example: "2023-01-01T10:00:00Z"
    PersonalInfo:
      type: object
      description: Personal information for KYC
      required:
        - firstName
        - lastName
        - dateOfBirth
      properties:
        firstName:
          type: string
          description: First name
          example: "John"
        lastName:
          type: string
          description: Last name
          example: "Doe"
        middleName:
          type: string
          description: Middle name
          example: "Michael"
        dateOfBirth:
          type: string
          format: date
          description: Date of birth
          example: "1990-01-15"
        nationality:
          type: string
          description: Nationality
          example: "British"
        address:
          $ref: '#/components/schemas/Address'
    BusinessInfo:
      type: object
      description: Business information for KYB
      required:
        - companyName
        - companyNumber
        - address
      properties:
        companyName:
          type: string
          description: Company name
          example: "Acme Corporation Ltd"
        companyNumber:
          type: string
          description: Company registration number
          example: "12345678"
        address:
          $ref: '#/components/schemas/Address'
        domain:
          type: string
          description: Company domain
          example: "acme.com"
        representatives:
          type: array
          items:
            $ref: '#/components/schemas/RepresentativeInfo'
    RepresentativeInfo:
      type: object
      description: Representative information
      required:
        - type
        - personalInfo
      properties:
        type:
          $ref: '#/components/schemas/RepresentativeType'
        personalInfo:
          $ref: '#/components/schemas/PersonalInfo'
    Address:
      type: object
      description: Address information
      required:
        - countryCode
      properties:
        street:
          type: string
          description: Street address
          example: "123 Main Street"
        city:
          type: string
          description: City
          example: "London"
        state:
          type: string
          description: State or region
          example: "England"
        postalCode:
          type: string
          description: Postal code
          example: "SW1A 1AA"
        countryCode:
          type: string
          description: ISO country code
          example: "GB"
    # Enum schemas
    VerificationType:
      type: string
      enum:
        - KYC
        - KYB
      description: Type of verification
      example: KYC
    VerificationStatus:
      type: string
      enum:
        - INITIATED
        - DOCUMENTS_REQUIRED
        - DOCUMENTS_UPLOADED
        - IN_PROGRESS
        - UNDER_REVIEW
        - COMPLETED
        - APPROVED
        - REJECTED
        - EXPIRED
      description: Verification status
      example: INITIATED
    RiskLevel:
      type: string
      enum:
        - LOW
        - MEDIUM
        - HIGH
        - CRITICAL
      description: Risk level assessment
      example: LOW
    DocumentType:
      type: string
      enum:
        - PASSPORT
        - DRIVING_LICENSE
        - NATIONAL_ID
        - UTILITY_BILL
        - BANK_STATEMENT
        - COMPANY_REGISTRATION
        - MEMORANDUM_OF_ASSOCIATION
        - ARTICLES_OF_ASSOCIATION
      description: Document type for verification
      example: PASSPORT
    DocumentStatus:
      type: string
      enum:
        - UPLOADED
        - PROCESSING
        - VERIFIED
        - REJECTED
      description: Document verification status
      example: UPLOADED
    RepresentativeType:
      type: string
      enum:
        - DIRECTOR
        - IDENTITY
        - OWNERSHIP
        - DECLARATORY
        - PRINCIPAL
        - DIRECTOR_COMPANY
      description: Representative type as per GBG classification
      example: DIRECTOR
    VerificationStep:
      type: object
      description: Verification step information
      properties:
        stepType:
          type: string
          enum:
            - DOCUMENT_UPLOAD
            - IDENTITY_VERIFICATION
            - LIVENESS_CHECK
            - DIGITAL_FOOTPRINT
            - RISK_ASSESSMENT
          description: Type of verification step
          example: "DOCUMENT_UPLOAD"
        status:
          type: string
          enum:
            - PENDING
            - IN_PROGRESS
            - COMPLETED
            - FAILED
          description: Step status
          example: "COMPLETED"
        completedAt:
          type: string
          format: date-time
          description: Step completion timestamp
          example: "2023-01-01T11:00:00Z"
    # GDPR Schemas
    SarRequest:
      type: object
      description: Subject Access Request
      required:
        - requestId
        - subtaskId
        - email
      properties:
        requestId:
          type: string
          description: Request ID
          example: "REQ-1234"
        subtaskId:
          type: string
          description: Sub Task ID
          example: "e58ed763-928c-4155-bee9-fdbaaadc15a5"
        email:
          type: string
          description: User email address
          example: "<EMAIL>"
    SarResponse:
      type: object
      description: Subject Access Response
      required:
        - requestId
        - subtaskId
      properties:
        requestId:
          type: string
          description: Request ID
          example: "REQ-1234"
        subtaskId:
          type: string
          description: Sub Task ID
          example: "e58ed763-928c-4155-bee9-fdbaaadc15a5"
        error:
          type: boolean
          description: Error flag
          default: false
          example: false
        reason:
          type: string
          description: Error reason if any
          example: "Request processed successfully"
        salutation:
          type: string
          description: User salutation
          example: "Mr."
        personalDataSegments:
          type: array
          items:
            $ref: '#/components/schemas/PersonalDataSegment'
    DdrRequest:
      type: object
      description: Data Deletion Request
      required:
        - requestId
        - subtaskId
        - email
      properties:
        requestId:
          type: string
          description: Request ID
          example: "REQ-1234"
        subtaskId:
          type: string
          description: Sub Task ID
          example: "e58ed763-928c-4155-bee9-fdbaaadc15a5"
        email:
          type: string
          description: User email address
          example: "<EMAIL>"
    DdrResponse:
      type: object
      description: Data Deletion Response
      required:
        - requestId
        - subtaskId
      properties:
        requestId:
          type: string
          description: Request ID
          example: "REQ-1234"
        subtaskId:
          type: string
          description: Sub Task ID
          example: "e58ed763-928c-4155-bee9-fdbaaadc15a5"
        error:
          type: boolean
          description: Error flag
          default: false
          example: false
        reason:
          type: string
          description: Error reason if any
          example: "Data deleted successfully"
    PersonalDataSegment:
      type: object
      description: Personal data segment for GDPR response
      required:
        - title
        - description
        - values
      properties:
        title:
          type: string
          description: Data segment title
          example: "Identity Verification Records"
        description:
          type: string
          description: Data segment description
          example: "Records of identity verification attempts and results"
        values:
          type: array
          items:
            type: object
            additionalProperties:
              type: string
          description: Data values
          example: [{"name": "John Doe", "email": "<EMAIL>"}]
    VerificationRequestsPageResponse:
      type: object
      description: Paginated verification requests response
      properties:
        content:
          type: array
          items:
            $ref: '#/components/schemas/VerificationStatusResponse'
        totalElements:
          type: integer
          format: int64
          description: Total number of elements
          example: 100
        totalPages:
          type: integer
          description: Total number of pages
          example: 10
        size:
          type: integer
          description: Page size
          example: 10
        number:
          type: integer
          description: Current page number
          example: 0
    # Common Error Schema
    ApiError:
      type: object
      description: Standard API error response
      required:
        - timestamp
        - status
        - error
        - message
        - path
      properties:
        timestamp:
          type: string
          format: date-time
          description: Timestamp when the error occurred
          example: "2023-12-01T10:30:00Z"
        status:
          type: integer
          description: HTTP status code
          example: 400
        error:
          type: string
          description: Error type/category
          example: "Bad Request"
        message:
          type: string
          description: Error message
          example: "Validation failed for request"
        type:
          type: string
          description: Problem type URI (RFC 7807)
          example: "https://api.gumtree.com/problems/validation-error"
        title:
          type: string
          description: Short, human-readable summary of the problem
          example: "Validation Error"
        detail:
          type: string
          description: Human-readable explanation specific to this occurrence
          example: "The firstName field is required but was not provided"
        instance:
          type: string
          description: URI reference that identifies the specific occurrence
          example: "/users/12345/identity-verification"
        code:
          type: string
          description: Application-specific error code
          example: "VALIDATION_FAILED"
        path:
          type: string
          description: Request path where error occurred
          example: "/users/12345/identity-verification"
        validationErrors:
          type: object
          additionalProperties:
            type: string
          description: Field-specific validation errors
          example:
            firstName: "First name is required"
            email: "Invalid email format"
        details:
          type: array
          items:
            $ref: '#/components/schemas/ApiErrorDetail'
          description: Detailed error information for specific fields
          example:
            - field: "firstName"
              message: "First name is required"
              location: "BODY"
        traceId:
          type: string
          description: Trace ID for debugging
          example: "abc123-def456-ghi789"

    ApiErrorDetail:
      type: object
      description: Detailed error information for specific fields or locations
      required:
        - field
        - message
        - location
      properties:
        field:
          type: string
          description: Field name that caused the error
          example: "firstName"
        message:
          type: string
          description: Error message for this field
          example: "First name is required"
        location:
          $ref: '#/components/schemas/LocationEnum'
        value:
          type: string
          description: The rejected value
          example: ""

    LocationEnum:
      type: string
      enum:
        - BODY
        - HEADER
        - PATH
        - QUERY
      description: Location where the error occurred
