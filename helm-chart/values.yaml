identity-verification-service:
  enabled: true

  image:
    repository: europe-west4-docker.pkg.dev/gum-host-7491/docker-artifact-repo
    branch: master
    name: identity-verification-service
    tagOverride: jenkins-pipeline-will-add-build-version

  pod:
    serviceAccountName: identity-verification-service-sa
    labels:
      buildnumber: "jenkins-pipeline-will-add-build-label"

  podmonitoring:
    enabled: true
    endpoints:
      - port: 8080
        path: /actuator/prometheus
        interval: 30s

  cloudSqlSideCar:
    enabled: true
    name: cloud-sql-proxy
    image:
      repository: europe-west4-docker.pkg.dev/gum-host-7491/docker-artifact-repo/gcr.io/cloudsql-docker/gce-proxy
      tag: 1.29.0
    databaseInstances:
      - name: tnspg-cloudsql-instance
        port: 5432
        region: europe-west4
    memory: 500Mi
    cpu: "250m"

  container:
    properties:
      GCP_LOGGING: "true"

    # Identity Verification Service specific secrets
    secrets:
      - name: DB_USER
        key: site-identityverification-jdbc-user
        version: latest
      - name: DB_PASSWORD
        key: site-identityverification-jdbc-password
        version: latest
      - name: GBG_API_KEY
        key: site-identity-verification-gbg-detected-api-key
        version: latest
      - name: GBG_BASE_URL
        key: site-identity-verification-gbg-detected-base-url
        version: latest

    livenessProbe:
      httpGet:
        path: /internal/health/liveness
        port: 8080
      initialDelaySeconds: 60
      periodSeconds: 30
      timeoutSeconds: 10
      failureThreshold: 3
    readinessProbe:
      httpGet:
        path: /internal/health/readiness
        port: 8080
      initialDelaySeconds: 30
      periodSeconds: 10
      timeoutSeconds: 5
      failureThreshold: 3

  hpa:
    enabled: true
    minReplicas: 2
    maxReplicas: 10
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80

  service:
    enabled: true
    ports:
      - protocol: TCP
        port: 80
        targetPort: 8080
        name: http

  virtualservice:
    enabled: true
    gateways:
      - istio-system/istio-internal-ingressgateway # Expose only through the Internal gateway
    hosts:
      - '{{ printf "%s.%s.%s.gumtree.cloud" .Release.Name .Values.region .Values.env }}'

# Database configuration
identity-verification-service-db:
  enabled: true

  image:
    repository: europe-west4-docker.pkg.dev/gum-host-7491/docker-artifact-repo/liquibase/liquibase
    tag: "4.17"

  pod:
    serviceAccountName: identity-verification-service-sa

  container:
    secrets:
      - name: DB_USER
        key: identity-verification-service-db-user
        version: latest
      - name: DB_PASSWORD
        key: identity-verification-service-db-password
        version: latest

    properties:
      LIQUIBASE_COMMAND_URL: "*****************************************************"
      LIQUIBASE_COMMAND_USERNAME: "$(DB_USER)"
      LIQUIBASE_COMMAND_PASSWORD: "$(DB_PASSWORD)"
      LIQUIBASE_COMMAND_CHANGELOG_FILE: "db/changelog/db.changelog-master.xml"
      LIQUIBASE_COMMAND_CONTEXTS: "production"

  cloudSqlSideCar:
    enabled: true
    name: cloud-sql-proxy
    image:
      repository: europe-west4-docker.pkg.dev/gum-host-7491/docker-artifact-repo/gcr.io/cloudsql-docker/gce-proxy
      tag: 1.29.0
    databaseInstances:
      - name: tnspg-cloudsql-instance
        port: 5432
        region: europe-west4
    memory: 512Mi
    cpu: 250m

# PostgreSQL configuration (for local development)
postgresql:
  enabled: false
  auth:
    postgresPassword: "password"
    username: "identityverification"
    password: "password"
    database: "identityverification"
  primary:
    persistence:
      enabled: true
      size: 8Gi
    http:
      - route:
          - destination:
              host: identity-verification-service
              port:
                number: 80

identity-verification-service-db:
  enabled: false

  image:
    repository: europe-west4-docker.pkg.dev/gum-host-7491/docker-artifact-repo/identity-verification-service-db
    tag: jenkins-pipeline-will-add-build-label

  container:
    properties:
      DB_ACTION: "updatesql"
      DB_URL: "*****************************************************"
    secrets:
      - name: DB_USER
        key: XXX-identity-verification-service-db-user
        version: latest
      - name: DB_PASSWORD
        key: XXX-identity-verification-service-db-password
        version: latest

  pod:
    serviceAccountName: identity-verification-service-sa

  cloudSqlProxy:
    name: cloud-sql-proxy
    image:
      repository: gcr.io/cloudsql-docker/gce-proxy
      tag: 1.33.1-buster
    region: europe-west4
    databaseInstance: tnspg-cloudsql-instance
    port: "5432"
    memory: 1Gi
    cpu: 500m