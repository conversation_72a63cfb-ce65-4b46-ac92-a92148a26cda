apiVersion: v2
name: identity-verification-service
description: A Helm chart for Identity Verification Service
type: application
version: 1.0.0
appVersion: "3.0-SNAPSHOT"

keywords:
  - identity
  - verification
  - kyc
  - kyb
  - gbg

home: https://github.com/gumtree/identity-verification-service
sources:
  - https://github.com/gumtree/identity-verification-service

maintainers:
  - name: Gumtree Platform Team
    email: <EMAIL>

dependencies:
  - name: application
    repository: "@gum"
    version: 0.6.*
    alias: identity-verification-service
    condition: identity-verification-service.enabled
  - name: liquibase
    repository: "@gum"
    version: 0.0.2
    alias: identity-verification-service-db
    condition: identity-verification-service-db.enabled
  - name: postgresql
    version: "11.9.13"
    repository: https://charts.bitnami.com/bitnami
    condition: postgresql.enabled
