# Identity Verification Service
Last release build: [![Build Status](https://jenkins.gum-ops-ci.gumtree.cloud/jenkins/job/Gumtree/job/identity-verification-service/badge/icon)](https://jenkins.gum-ops-ci.gumtree.cloud/jenkins/job/Gumtree/job/identity-verification-service/)

Contract build: [![Build Status](https://jenkins.gum-ops-ci.gumtree.cloud/jenkins/job/Gumtree/job/identity-verification-service-contract/badge/icon)](https://jenkins.gum-ops-ci.gumtree.cloud/jenkins/job/Gumtree/job/identity-verification-services-contract/)

[Spinnaker Pipelines](https://gt-spinnaker.gt-prod-ops.classifiedscloud.io/#/applications/identity-verification-service/executions)

[![Quality Gate Status](https://sonarqube-enterprise.es.ecg.tools/api/project_badges/measure?project=com.gumtree.tns.identity-verification-service%3Aserver&metric=alert_status)](https://sonarqube-enterprise.es.ecg.tools/dashboard?id=com.gumtree.tns.identity-verification-service%3Aserver)
[![Reliability Rating](https://sonarqube-enterprise.es.ecg.tools/api/project_badges/measure?project=com.gumtree.tns.identity-verification-service%3Aserver&metric=reliability_rating)](https://sonarqube-enterprise.es.ecg.tools/dashboard?id=com.gumtree.tns.identity-verification-service%3Aserver)
[![Security Rating](https://sonarqube-enterprise.es.ecg.tools/api/project_badges/measure?project=com.gumtree.tns.identity-verification-service%3Aserver&metric=security_rating)](https://sonarqube-enterprise.es.ecg.tools/dashboard?id=com.gumtree.tns.identity-verification-service%3Aserver)
[![Technical Debt](https://sonarqube-enterprise.es.ecg.tools/api/project_badges/measure?project=com.gumtree.tns.identity-verification-service%3Aserver&metric=sqale_index)](https://sonarqube-enterprise.es.ecg.tools/dashboard?id=com.gumtree.tns.identity-verification-service%3Aserver)
[![Vulnerabilities](https://sonarqube-enterprise.es.ecg.tools/api/project_badges/measure?project=com.gumtree.tns.identity-verification-service%3Aserver&metric=vulnerabilities)](https://sonarqube-enterprise.es.ecg.tools/dashboard?id=com.gumtree.tns.identity-verification-service%3Aserver)

## Introduction

The purpose of the project is to provide a :question:. 
Our primary task is to :question:

## How to build
- Clone project
- Make sure that it points to *master*
- Build the project

```
mvn -T 3C clean compile
```

## How to run locally
- In Intellij open [**GumtreeJettyMain.java**](server/src/main/java/com/gumtree/GumtreeJettyMain.java) and run it.

Properties will be loaded from application.properties. For details of how it's configured see below.

### Run using maven
Change to `server` directory and run `mvn exec:java`.

Use following command to pass custom properties values:

```
MAVEN_OPTS="$MAVEN_OPTS" mvn exec:java
```

## Properties
There is pre-defined property file in the project
- [application.properties](server/src/main/resources/application.properties)



## Modules
- [contract](contract) module contains description of the endpoints and the model for this project
- [server](server) is server module where we spin up the server for our project. It contains endpoint controllers and any business logic related to constructing responses.
- [helm-chart](helm-chart) module contains k8s config
- [qa-tests](qa-tests) module contains regression tests
