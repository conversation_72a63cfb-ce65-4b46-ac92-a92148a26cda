package com.gumtree;

import com.gumtree.shared.db.DbUpdate;

public class GumtreeDbMain extends DbUpdate {

    public static void main(String[] args) throws Exception {
        new GumtreeDbMain().run();
    }

    @Override
    public String getDbAction() {
        return System.getenv("DB_ACTION");
    }

    @Override
    public String getDbUrl() {
        return System.getenv("DB_URL");
    }

    @Override
    public String getDbUsername() {
        return System.getenv("DB_USER");
    }

    @Override
    public String getDbPassword() {
        return System.getenv("DB_PASSWORD");
    }

    @Override
    public String getChangeLogFile() {
        return "com/gumtree/liquibase/changelog-master.xml";
    }
}
