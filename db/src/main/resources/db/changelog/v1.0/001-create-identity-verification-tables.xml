<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="001-create-identity-verification-requests" author="identity-verification-service">
        <createTable tableName="identity_verification_requests">
            <column name="id" type="UUID" defaultValueComputed="gen_random_uuid()">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="user_id" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="account_id" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="verification_type" type="VARCHAR(10)">
                <constraints nullable="false"/>
            </column>
            <column name="status" type="VARCHAR(20)">
                <constraints nullable="false"/>
            </column>
            <column name="risk_score" type="INTEGER">
                <constraints nullable="true"/>
            </column>
            <column name="risk_level" type="VARCHAR(10)">
                <constraints nullable="true"/>
            </column>
            <column name="gbg_profile_id" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
            <column name="customer_reference" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
            <column name="personal_info" type="JSONB">
                <constraints nullable="true"/>
            </column>
            <column name="business_info" type="JSONB">
                <constraints nullable="true"/>
            </column>
            <column name="created_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="completed_at" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="true"/>
            </column>
        </createTable>
        
        <addCheckConstraint tableName="identity_verification_requests" 
                           constraintName="chk_user_or_account_id" 
                           checkCondition="(user_id IS NOT NULL AND account_id IS NULL) OR (user_id IS NULL AND account_id IS NOT NULL)"/>
        
        <addCheckConstraint tableName="identity_verification_requests" 
                           constraintName="chk_verification_type" 
                           checkCondition="verification_type IN ('KYC', 'KYB')"/>
        
        <addCheckConstraint tableName="identity_verification_requests" 
                           constraintName="chk_status" 
                           checkCondition="status IN ('INITIATED', 'DOCUMENTS_REQUIRED', 'DOCUMENTS_UPLOADED', 'IN_PROGRESS', 'UNDER_REVIEW', 'COMPLETED', 'APPROVED', 'REJECTED', 'EXPIRED')"/>
        
        <addCheckConstraint tableName="identity_verification_requests" 
                           constraintName="chk_risk_level" 
                           checkCondition="risk_level IS NULL OR risk_level IN ('LOW', 'MEDIUM', 'HIGH', 'CRITICAL')"/>
    </changeSet>

    <changeSet id="002-create-verification-steps" author="identity-verification-service">
        <createTable tableName="verification_steps">
            <column name="id" type="UUID" defaultValueComputed="gen_random_uuid()">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="verification_request_id" type="UUID">
                <constraints nullable="false" foreignKeyName="fk_verification_steps_request" references="identity_verification_requests(id)"/>
            </column>
            <column name="step_type" type="VARCHAR(30)">
                <constraints nullable="false"/>
            </column>
            <column name="step_status" type="VARCHAR(20)">
                <constraints nullable="false"/>
            </column>
            <column name="gbg_verification_id" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
            <column name="provider_data" type="JSONB">
                <constraints nullable="true"/>
            </column>
            <column name="created_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="completed_at" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="true"/>
            </column>
        </createTable>
        
        <addCheckConstraint tableName="verification_steps" 
                           constraintName="chk_step_type" 
                           checkCondition="step_type IN ('DOCUMENT_UPLOAD', 'IDENTITY_VERIFICATION', 'LIVENESS_CHECK', 'DIGITAL_FOOTPRINT', 'RISK_ASSESSMENT')"/>
        
        <addCheckConstraint tableName="verification_steps" 
                           constraintName="chk_step_status" 
                           checkCondition="step_status IN ('PENDING', 'IN_PROGRESS', 'COMPLETED', 'FAILED')"/>
    </changeSet>

    <changeSet id="003-create-verification-documents" author="identity-verification-service">
        <createTable tableName="verification_documents">
            <column name="id" type="UUID" defaultValueComputed="gen_random_uuid()">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="verification_request_id" type="UUID">
                <constraints nullable="false" foreignKeyName="fk_verification_documents_request" references="identity_verification_requests(id)"/>
            </column>
            <column name="document_type" type="VARCHAR(30)">
                <constraints nullable="false"/>
            </column>
            <column name="document_status" type="VARCHAR(20)">
                <constraints nullable="false"/>
            </column>
            <column name="document_url" type="VARCHAR(500)">
                <constraints nullable="true"/>
            </column>
            <column name="gbg_document_id" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
            <column name="file_name" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
            <column name="file_size" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="mime_type" type="VARCHAR(100)">
                <constraints nullable="true"/>
            </column>
            <column name="description" type="TEXT">
                <constraints nullable="true"/>
            </column>
            <column name="created_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
        
        <addCheckConstraint tableName="verification_documents" 
                           constraintName="chk_document_type" 
                           checkCondition="document_type IN ('PASSPORT', 'DRIVING_LICENSE', 'NATIONAL_ID', 'UTILITY_BILL', 'BANK_STATEMENT', 'COMPANY_REGISTRATION', 'MEMORANDUM_OF_ASSOCIATION', 'ARTICLES_OF_ASSOCIATION')"/>
        
        <addCheckConstraint tableName="verification_documents" 
                           constraintName="chk_document_status" 
                           checkCondition="document_status IN ('UPLOADED', 'PROCESSING', 'VERIFIED', 'REJECTED')"/>
    </changeSet>

</databaseChangeLog>
