<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="004-create-representatives" author="identity-verification-service">
        <createTable tableName="representatives">
            <column name="id" type="UUID" defaultValueComputed="gen_random_uuid()">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="verification_request_id" type="UUID">
                <constraints nullable="false" foreignKeyName="fk_representatives_request" references="identity_verification_requests(id)"/>
            </column>
            <column name="gbg_representative_id" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
            <column name="representative_type" type="VARCHAR(20)">
                <constraints nullable="false"/>
            </column>
            <column name="personal_info" type="JSONB">
                <constraints nullable="false"/>
            </column>
            <column name="verification_status" type="VARCHAR(20)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
        
        <addCheckConstraint tableName="representatives" 
                           constraintName="chk_representative_type" 
                           checkCondition="representative_type IN ('DIRECTOR', 'IDENTITY', 'OWNERSHIP', 'DECLARATORY', 'PRINCIPAL', 'DIRECTOR_COMPANY')"/>
        
        <addCheckConstraint tableName="representatives" 
                           constraintName="chk_verification_status" 
                           checkCondition="verification_status IN ('PENDING', 'IN_PROGRESS', 'VERIFIED', 'REJECTED')"/>
    </changeSet>

    <changeSet id="005-create-risk-assessments" author="identity-verification-service">
        <createTable tableName="risk_assessments">
            <column name="id" type="UUID" defaultValueComputed="gen_random_uuid()">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="verification_request_id" type="UUID">
                <constraints nullable="false" foreignKeyName="fk_risk_assessments_request" references="identity_verification_requests(id)"/>
            </column>
            <column name="risk_factors" type="JSONB">
                <constraints nullable="false"/>
            </column>
            <column name="risk_score" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="risk_level" type="VARCHAR(10)">
                <constraints nullable="false"/>
            </column>
            <column name="assessment_details" type="JSONB">
                <constraints nullable="true"/>
            </column>
            <column name="assessment_date" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
        
        <addCheckConstraint tableName="risk_assessments" 
                           constraintName="chk_risk_score" 
                           checkCondition="risk_score >= 0 AND risk_score <= 100"/>
        
        <addCheckConstraint tableName="risk_assessments" 
                           constraintName="chk_risk_level_assessment" 
                           checkCondition="risk_level IN ('LOW', 'MEDIUM', 'HIGH', 'CRITICAL')"/>
    </changeSet>

    <changeSet id="006-create-audit-logs" author="identity-verification-service">
        <createTable tableName="audit_logs">
            <column name="id" type="UUID" defaultValueComputed="gen_random_uuid()">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="verification_request_id" type="UUID">
                <constraints nullable="true" foreignKeyName="fk_audit_logs_request" references="identity_verification_requests(id)"/>
            </column>
            <column name="action_type" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="action_details" type="JSONB">
                <constraints nullable="true"/>
            </column>
            <column name="performed_by" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
            <column name="user_id" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="account_id" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="ip_address" type="INET">
                <constraints nullable="true"/>
            </column>
            <column name="user_agent" type="TEXT">
                <constraints nullable="true"/>
            </column>
            <column name="session_id" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
            <column name="created_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="007-create-gbg-webhook-events" author="identity-verification-service">
        <createTable tableName="gbg_webhook_events">
            <column name="id" type="UUID" defaultValueComputed="gen_random_uuid()">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="verification_request_id" type="UUID">
                <constraints nullable="true" foreignKeyName="fk_gbg_webhook_events_request" references="identity_verification_requests(id)"/>
            </column>
            <column name="event_type" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="gbg_profile_id" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
            <column name="event_data" type="JSONB">
                <constraints nullable="false"/>
            </column>
            <column name="processed" type="BOOLEAN" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
            <column name="processing_error" type="TEXT">
                <constraints nullable="true"/>
            </column>
            <column name="received_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="processed_at" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="true"/>
            </column>
        </createTable>
    </changeSet>

</databaseChangeLog>
