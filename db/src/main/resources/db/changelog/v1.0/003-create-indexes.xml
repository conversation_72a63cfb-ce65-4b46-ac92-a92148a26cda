<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="008-create-indexes-identity-verification-requests" author="identity-verification-service">
        <createIndex tableName="identity_verification_requests" indexName="idx_identity_verification_requests_user_id">
            <column name="user_id"/>
        </createIndex>
        
        <createIndex tableName="identity_verification_requests" indexName="idx_identity_verification_requests_account_id">
            <column name="account_id"/>
        </createIndex>
        
        <createIndex tableName="identity_verification_requests" indexName="idx_identity_verification_requests_status">
            <column name="status"/>
        </createIndex>
        
        <createIndex tableName="identity_verification_requests" indexName="idx_identity_verification_requests_verification_type">
            <column name="verification_type"/>
        </createIndex>
        
        <createIndex tableName="identity_verification_requests" indexName="idx_identity_verification_requests_gbg_profile_id">
            <column name="gbg_profile_id"/>
        </createIndex>
        
        <createIndex tableName="identity_verification_requests" indexName="idx_identity_verification_requests_customer_reference">
            <column name="customer_reference"/>
        </createIndex>
        
        <createIndex tableName="identity_verification_requests" indexName="idx_identity_verification_requests_created_at">
            <column name="created_at"/>
        </createIndex>
    </changeSet>

    <changeSet id="009-create-indexes-verification-steps" author="identity-verification-service">
        <createIndex tableName="verification_steps" indexName="idx_verification_steps_verification_request_id">
            <column name="verification_request_id"/>
        </createIndex>
        
        <createIndex tableName="verification_steps" indexName="idx_verification_steps_step_type">
            <column name="step_type"/>
        </createIndex>
        
        <createIndex tableName="verification_steps" indexName="idx_verification_steps_step_status">
            <column name="step_status"/>
        </createIndex>
        
        <createIndex tableName="verification_steps" indexName="idx_verification_steps_gbg_verification_id">
            <column name="gbg_verification_id"/>
        </createIndex>
    </changeSet>

    <changeSet id="010-create-indexes-verification-documents" author="identity-verification-service">
        <createIndex tableName="verification_documents" indexName="idx_verification_documents_verification_request_id">
            <column name="verification_request_id"/>
        </createIndex>
        
        <createIndex tableName="verification_documents" indexName="idx_verification_documents_document_type">
            <column name="document_type"/>
        </createIndex>
        
        <createIndex tableName="verification_documents" indexName="idx_verification_documents_document_status">
            <column name="document_status"/>
        </createIndex>
        
        <createIndex tableName="verification_documents" indexName="idx_verification_documents_gbg_document_id">
            <column name="gbg_document_id"/>
        </createIndex>
    </changeSet>

    <changeSet id="011-create-indexes-representatives" author="identity-verification-service">
        <createIndex tableName="representatives" indexName="idx_representatives_verification_request_id">
            <column name="verification_request_id"/>
        </createIndex>
        
        <createIndex tableName="representatives" indexName="idx_representatives_gbg_representative_id">
            <column name="gbg_representative_id"/>
        </createIndex>
        
        <createIndex tableName="representatives" indexName="idx_representatives_representative_type">
            <column name="representative_type"/>
        </createIndex>
        
        <createIndex tableName="representatives" indexName="idx_representatives_verification_status">
            <column name="verification_status"/>
        </createIndex>
    </changeSet>

    <changeSet id="012-create-indexes-risk-assessments" author="identity-verification-service">
        <createIndex tableName="risk_assessments" indexName="idx_risk_assessments_verification_request_id">
            <column name="verification_request_id"/>
        </createIndex>
        
        <createIndex tableName="risk_assessments" indexName="idx_risk_assessments_risk_level">
            <column name="risk_level"/>
        </createIndex>
        
        <createIndex tableName="risk_assessments" indexName="idx_risk_assessments_risk_score">
            <column name="risk_score"/>
        </createIndex>
        
        <createIndex tableName="risk_assessments" indexName="idx_risk_assessments_assessment_date">
            <column name="assessment_date"/>
        </createIndex>
    </changeSet>

    <changeSet id="013-create-indexes-audit-logs" author="identity-verification-service">
        <createIndex tableName="audit_logs" indexName="idx_audit_logs_verification_request_id">
            <column name="verification_request_id"/>
        </createIndex>
        
        <createIndex tableName="audit_logs" indexName="idx_audit_logs_action_type">
            <column name="action_type"/>
        </createIndex>
        
        <createIndex tableName="audit_logs" indexName="idx_audit_logs_user_id">
            <column name="user_id"/>
        </createIndex>
        
        <createIndex tableName="audit_logs" indexName="idx_audit_logs_account_id">
            <column name="account_id"/>
        </createIndex>
        
        <createIndex tableName="audit_logs" indexName="idx_audit_logs_created_at">
            <column name="created_at"/>
        </createIndex>
    </changeSet>

    <changeSet id="014-create-indexes-gbg-webhook-events" author="identity-verification-service">
        <createIndex tableName="gbg_webhook_events" indexName="idx_gbg_webhook_events_verification_request_id">
            <column name="verification_request_id"/>
        </createIndex>
        
        <createIndex tableName="gbg_webhook_events" indexName="idx_gbg_webhook_events_event_type">
            <column name="event_type"/>
        </createIndex>
        
        <createIndex tableName="gbg_webhook_events" indexName="idx_gbg_webhook_events_gbg_profile_id">
            <column name="gbg_profile_id"/>
        </createIndex>
        
        <createIndex tableName="gbg_webhook_events" indexName="idx_gbg_webhook_events_processed">
            <column name="processed"/>
        </createIndex>
        
        <createIndex tableName="gbg_webhook_events" indexName="idx_gbg_webhook_events_received_at">
            <column name="received_at"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
