<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">
    <!--  You can refer to the saved-search configuration:https://github.com/gumtree-tech/saved-search/blob/master/db/src/main/resources/com/gumtree/liquibase/changelog/001-db-structure-init.xml  -->
    <changeSet id="create-table" author="jack.zhang">
        <createTable tableName="test">
            <column name="id" type="TEXT">
                <constraints primaryKey="true" primaryKeyName="pk_test"/>
            </column>
            <column name="test_text" type="TEXT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
</databaseChangeLog>
