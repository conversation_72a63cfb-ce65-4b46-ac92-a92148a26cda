# identity-verification-service Database Update

## Executing DB schema update/rollback

### Requirements:
- PostgreSQL 13 server is running and is accessible on `localhost:5432`
- database `identity-verification-service` is created
- user `identity-verification-service-rw` is created and has all privileges to `identity-verification-service` and the user can log in
- password for `identity-verification-service-rw` is `identity-verification-service-rw`

See below how to set up database with Docker

### Running Liquibase


Execute [GumtreeDbMain](src/main/java/com/gumtree/GumtreeDbMain.java) with following env variables

```
DB_ACTION=update;DB_URL=****************************************************************************************************************************************************
```

`DB_ACTION` controls action to execute. Available options:
- `update` - executes update
- `rollback [int:how many changes to rollback | optional | default 1]` - executes rollback
- `display-update-sql` - display update sql without executing it

## Setting up database with Docker

### Start postgres

```
docker pull postgres:13-alpine
mkdir ~/identity-verification-service-postgres-data
docker run --name local-postgres -p 5432:5432 -v ~/identity-verification-service-postgres-data:/var/lib/postgresql/data -e POSTGRES_PASSWORD=mysecretpassword postgres:13-alpine
```

### Create database

```
docker exec -it local-postgres /bin/bash
psql -d postgres -U postgres
```

```
create database identity-verification-service;
create role "identity-verification-service-rw";
grant all privileges on database  identity-verification-service to "identity-verification-service-rw";
ALTER ROLE "identity-verification-service-rw" WITH PASSWORD 'identity-verification-service-rw';
ALTER ROLE "identity-verification-service-rw" WITH LOGIN;
```

## Executing DB schema update with Docker

### Build `db` docker image

```
cd db
mvn compile jib:dockerBuild -Dimage=identity-verification-service-db
```

### Execute DB update

```
docker run --network=host  -e DB_ACTION='update' -e DB_PASSWORD='identity-verification-service-rw' -e DB_URL='**************************************************************' -e DB_USER='identity-verification-service-rw' identity-verification-service-db
```

