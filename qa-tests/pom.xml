<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>com.gumtree.shared-gcp-maven-parent</groupId>
        <artifactId>shared-gcp-maven-parent-java</artifactId>
        <version>5.24.d278c41</version>
        <relativePath/>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.gumtree.tns.identity-verification-service</groupId>
    <artifactId>qa-tests</artifactId>
    <version>3.0-SNAPSHOT</version>

    <name>Identity Verification Service QA Tests</name>
    <description>Identity Verification Service End to End Tests</description>

    <properties>
        <gt.tds.contract.version>5.26.ae0c7d9</gt.tds.contract.version>
        <gt.shared-commons.properties.version>5.9</gt.shared-commons.properties.version>
    </properties>

    <profiles>
        <profile>
            <id>local</id>
            <build>
                <defaultGoal>clean verify</defaultGoal>
            </build>
            <properties>
                <argLine>-Dgumtree.identity-verification-service.server.url=http://localhost:8080 -Dgumtree.test.data.server.url=http://gumtree-test-data-service.gt</argLine>
            </properties>
        </profile>
    </profiles>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.gumtree.shared-gcp-maven-bom</groupId>
                <artifactId>shared-gcp-maven-bom-chassis</artifactId>
                <version>2024-12-10-14-45-56.master.1035670</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.gumtree.shared-gcp-maven-bom</groupId>
                <artifactId>shared-gcp-maven-bom-http</artifactId>
                <version>2024-10-09-15-32-24.master.3fcf51e</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- Force HdrHistogram version to resolve dependency convergence issue -->
            <dependency>
                <groupId>org.hdrhistogram</groupId>
                <artifactId>HdrHistogram</artifactId>
                <version>2.1.12</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>

        <dependency>
            <groupId>com.gumtree.tns.identity-verification-service</groupId>
            <artifactId>contract</artifactId>
            <version>${project.version}</version>
            <type>yaml</type>
        </dependency>

        <dependency>
            <groupId>com.gumtree.service.testdata</groupId>
            <artifactId>contract</artifactId>
            <version>${gt.tds.contract.version}</version>
            <type>yaml</type>
        </dependency>

        <dependency>
            <groupId>com.gumtree.shared-commons.properties</groupId>
            <artifactId>gtprops</artifactId>
            <version>${gt.shared-commons.properties.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.netflix.archaius</groupId>
                    <artifactId>archaius-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>jsr305</artifactId>
        </dependency>

        <!-- Logging dependencies -->

        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
        </dependency>

        <!-- Feign dependencies -->
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-core</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-hystrix</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-slf4j</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-jackson</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-okhttp</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign.form</groupId>
            <artifactId>feign-form</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-jaxb</artifactId>
        </dependency>

        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.oltu.oauth2</groupId>
            <artifactId>org.apache.oltu.oauth2.client</artifactId>
            <version>1.0.2</version>
        </dependency>

        <dependency>
            <groupId>javax.annotation</groupId>
            <artifactId>javax.annotation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.openapitools</groupId>
            <artifactId>jackson-databind-nullable</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>

        <!-- Jackson dependencies -->

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jdk8</artifactId>
        </dependency>

        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>

        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
        </dependency>

        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.hamcrest</groupId>
            <artifactId>hamcrest-library</artifactId>
        </dependency>

        <dependency>
            <groupId>org.awaitility</groupId>
            <artifactId>awaitility</artifactId>
        </dependency>
        <dependency>
            <groupId>org.assertj</groupId>
            <artifactId>assertj-core</artifactId>
            <version>3.25.3</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <version>1.12</version>
                <executions>
                    <execution>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>add-source</goal>
                        </goals>
                        <configuration>
                            <sources>
                                <source>${project.build.directory}/generated-sources/src/main/java</source>
                                <source>${project.build.directory}/generated-sources/src/main/resources</source>
                            </sources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <version>3.1.2</version>
                <executions>
                    <execution>
                        <id>unpack-shared-resources</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>unpack-dependencies</goal>
                        </goals>
                        <configuration>
                            <includeGroupIds>com.gumtree</includeGroupIds>
                            <includeArtifactIds>shared-feign-hystrix-api-client</includeArtifactIds>
                            <outputDirectory>${project.build.directory}/shared-resources</outputDirectory>
                        </configuration>
                    </execution>
                    <execution>
                        <id>copy-contracts</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <includeTypes>yaml</includeTypes>
                            <outputDirectory>${project.build.directory}/contracts</outputDirectory>
                            <stripVersion>true</stripVersion>
                            <prependGroupId>true</prependGroupId>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.openapitools</groupId>
                <artifactId>openapi-generator-maven-plugin</artifactId>
                <version>4.2.2</version>
                <executions>
                    <execution>
                        <id>tds-contract</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.build.directory}/contracts/com.gumtree.service.testdata.contract.yaml</inputSpec>
                            <generatorName>java</generatorName>
                            <output>${project.build.directory}/generated-sources</output>
                            <modelPackage>com.gumtree.testdata.service.model</modelPackage>
                            <generateModels>true</generateModels>
                            <generateModelTests>false</generateModelTests>
                            <apiPackage>com.gumtree.testdata.service</apiPackage>
                            <generateApis>true</generateApis>
                            <generateApiTests>false</generateApiTests>
                            <generateSupportingFiles>true</generateSupportingFiles>
                            <skipValidateSpec>true</skipValidateSpec>
                            <configOptions>
                                <library>feign</library>
                                <serializableModel>true</serializableModel>
                                <interfaceOnly>true</interfaceOnly>
                                <skipDefaultInterface>true</skipDefaultInterface>
                                <dateLibrary>java8</dateLibrary>
                            </configOptions>
                            <addCompileSourceRoot>true</addCompileSourceRoot>
                            <configHelp>false</configHelp>
                            <invokerPackage>com.gumtree.testdata.service.api</invokerPackage>
                            <httpUserAgent>identity-verification-service-tds-generated-client</httpUserAgent>
                        </configuration>
                    </execution>
                    <execution>
                        <id>identity-verification-service-contract</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.basedir}/../contract/contract.yaml</inputSpec>
                            <generatorName>java</generatorName>
                            <output>${project.build.directory}/generated-sources</output>
                            <modelPackage>com.gumtree.tns.identityverification.model</modelPackage>
                            <generateModels>true</generateModels>
                            <generateModelTests>false</generateModelTests>
                            <apiPackage>com.gumtree.tns.identityverification.service</apiPackage>
                            <generateApis>true</generateApis>
                            <generateApiTests>false</generateApiTests>
                            <generateSupportingFiles>true</generateSupportingFiles>
                            <configOptions>
                                <library>feign</library>
                                <serializableModel>true</serializableModel>
                                <interfaceOnly>true</interfaceOnly>
                                <skipDefaultInterface>true</skipDefaultInterface>
                                <dateLibrary>java8</dateLibrary>
                            </configOptions>
                            <addCompileSourceRoot>true</addCompileSourceRoot>
                            <configHelp>false</configHelp>
                            <invokerPackage>com.gumtree.tns.identityverification.service.api</invokerPackage>
                            <httpUserAgent>identity-verification-service-qa-tests-generated-client</httpUserAgent>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.22.2</version>
            </plugin>
        </plugins>
    </build>
</project>
