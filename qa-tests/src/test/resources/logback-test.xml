<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d %green([%thread]) %highlight(%level) %logger{50} - %msg%n</pattern>
        </encoder>
    </appender>

    <logger name="feign.Logger" level="DEBUG" />

    <root level="INFO">
        <appender-ref ref="STDOUT"/>
    </root>

</configuration>
