package com.gumtree.tns;

import com.gumtree.tns.identityverification.model.VerificationStatusResponse;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

public class SampleTest extends BaseTest {

    @Test
    @DisplayName("Should say hello..")
    public void shouldSayHello() {
        // given
        var user = testDataApi.createUniqueUser();
        var name = "tony";

        // when
        // var response = identityVerificationApi.getUserVerificationStatus(userId);

        // then
        // assertThat(response).isEqualTo(new VerificationStatusResponse());
    }
}
