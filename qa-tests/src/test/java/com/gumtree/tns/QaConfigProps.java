package com.gumtree.tns;

import com.gumtree.common.properties.GtProps;

public enum QaConfigProps implements GtProps.GtProperty {

    SERVICE("gumtree.identity-verification-service.server.url", ""),
    TDS("gumtree.test.data.server.url", "");

    private final String name;
    private final String description;

    QaConfigProps(String name, String description) {
        this.name = name;
        this.description = description;
    }

    @Override
    public String getPropertyName() {
        return name;
    }

    @Override
    public String getDescription() {
        return description;
    }
}
