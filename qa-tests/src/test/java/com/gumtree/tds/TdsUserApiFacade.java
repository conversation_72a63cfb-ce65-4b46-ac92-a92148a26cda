package com.gumtree.tds;

import com.gumtree.testdata.service.UsersApi;
import com.gumtree.testdata.service.model.TestUser;
import com.gumtree.testdata.service.model.User;

public class TdsUserApiFacade {

    private UsersApi usersApi;

    public TdsUserApiFacade(UsersApi usersApi) {
        this.usersApi = usersApi;
    }

    public User createUniqueUser() {
        TestUser testUser = new TestUser();
        return usersApi.createUser(testUser);
    }

    public User createUser(TestUser testUser) {
        return usersApi.createUser(testUser);
    }

}
